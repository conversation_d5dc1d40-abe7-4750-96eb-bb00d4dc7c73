import { AllocationManager } from '../../../src/modules/patrimony/AllocationManager';
import { PatrimonySync } from '../../../src/modules/patrimony/PatrimonySync';

// Mock DOM elements
const createMockElement = (
  index: number,
  category: string = 'Stocks',
  subcategory: string = 'Tech'
): HTMLElement => {
  const element = document.createElement('div');
  element.dataset.category = category;
  element.dataset.subcategory = subcategory;
  element.dataset.maxAllowed = '50';
  element.dataset.initialValue = '0';

  const input = document.createElement('input');
  input.setAttribute('data-allocation-input', '');
  input.type = 'text';
  element.appendChild(input);

  const slider = document.createElement('input');
  slider.setAttribute('data-allocation-slider', '');
  slider.type = 'range';
  slider.min = '0';
  slider.max = '100';
  element.appendChild(slider);

  const percentageDisplay = document.createElement('span');
  percentageDisplay.setAttribute('data-percentage-display', '');
  element.appendChild(percentageDisplay);

  const valueDisplay = document.createElement('span');
  valueDisplay.setAttribute('data-value-display', '');
  element.appendChild(valueDisplay);

  return element;
};

const createMockContainer = (itemCount: number = 2): HTMLElement => {
  const container = document.createElement('div');
  container.setAttribute('data-allocation-container', '');

  for (let i = 0; i < itemCount; i++) {
    const item = createMockElement(i, `Category${i}`, `Subcategory${i}`);
    item.setAttribute('data-allocation-item', '');
    container.appendChild(item);
  }

  return container;
};

describe('AllocationManager', () => {
  let allocationManager: AllocationManager;
  let patrimonySync: PatrimonySync;
  let container: HTMLElement;

  beforeEach(() => {
    // Reset PatrimonySync singleton
    (PatrimonySync as any).instance = undefined;
    patrimonySync = PatrimonySync.getInstance();
    patrimonySync.reset();
    patrimonySync.setMainValue(100000);

    // Create mock DOM
    container = createMockContainer(2);
    document.body.appendChild(container);

    allocationManager = new AllocationManager(patrimonySync);
  });

  afterEach(() => {
    allocationManager.destroy();
    document.body.removeChild(container);
    patrimonySync.removeAllListeners();
  });

  describe('Initialization', () => {
    it('should initialize items from DOM', () => {
      const mockCallback = jest.fn();
      allocationManager.on('initializationCompleted', mockCallback);

      allocationManager.initializeItems('[data-allocation-container]', '[data-allocation-item]');

      expect(mockCallback).toHaveBeenCalledWith({
        itemCount: 2,
        items: [0, 1],
      });
      expect(allocationManager.isReady()).toBe(true);
    });

    it('should emit itemInitialized for each item', () => {
      const mockCallback = jest.fn();
      allocationManager.on('itemInitialized', mockCallback);

      allocationManager.initializeItems('[data-allocation-container]', '[data-allocation-item]');

      expect(mockCallback).toHaveBeenCalledTimes(2);
      expect(mockCallback).toHaveBeenCalledWith({
        index: 0,
        category: 'Category0',
        subcategory: 'Subcategory0',
        maxAllowed: 50,
        initialValue: 0,
        element: expect.any(HTMLElement),
      });
    });

    it('should throw error if container not found', () => {
      expect(() => {
        allocationManager.initializeItems('[data-nonexistent]');
      }).toThrow('Container not found: [data-nonexistent]');
    });

    it('should handle missing items gracefully', () => {
      const emptyContainer = document.createElement('div');
      emptyContainer.setAttribute('data-allocation-container', '');
      document.body.appendChild(emptyContainer);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      allocationManager.initializeItems('[data-allocation-container]', '[data-nonexistent-item]');

      expect(consoleSpy).toHaveBeenCalledWith(
        'No allocation items found with selector: [data-nonexistent-item]'
      );

      consoleSpy.mockRestore();
      document.body.removeChild(emptyContainer);
    });
  });

  describe('Item Management', () => {
    beforeEach(() => {
      allocationManager.initializeItems('[data-allocation-container]', '[data-allocation-item]');
    });

    it('should update allocation when input changes', () => {
      const mockCallback = jest.fn();
      allocationManager.on('itemUpdated', mockCallback);

      allocationManager.updateAllocation(0, 25000);

      expect(mockCallback).toHaveBeenCalledWith({
        index: 0,
        value: 25000,
        allocation: expect.objectContaining({
          index: 0,
          value: 25000,
          percentage: 25,
        }),
      });
    });

    it('should get allocation by index', () => {
      allocationManager.updateAllocation(0, 30000);

      const allocation = allocationManager.getAllocation(0);
      expect(allocation).toEqual(
        expect.objectContaining({
          index: 0,
          value: 30000,
          percentage: 30,
        })
      );
    });

    it('should get all allocations', () => {
      allocationManager.updateAllocation(0, 25000);
      allocationManager.updateAllocation(1, 35000);

      const allocations = allocationManager.getAllocations();
      expect(allocations).toHaveLength(2);
      expect(allocations[0]?.value).toBe(25000);
      expect(allocations[1]?.value).toBe(35000);
    });

    it('should get item element by index', () => {
      const element = allocationManager.getItemElement(0);
      expect(element).toBeInstanceOf(HTMLElement);
      expect(element?.dataset.category).toBe('Category0');
    });

    it('should get item input by index', () => {
      const input = allocationManager.getItemInput(0);
      expect(input).toBeInstanceOf(HTMLInputElement);
      expect(input?.getAttribute('data-allocation-input')).toBe('');
    });
  });

  describe('Validation and Overflow Prevention', () => {
    beforeEach(() => {
      allocationManager.initializeItems('[data-allocation-container]', '[data-allocation-item]');
      patrimonySync.updateValidationConfig({ allowOverflow: false });
    });

    it('should prevent overflow by adjusting value', () => {
      const mockCallback = jest.fn();
      allocationManager.on('overflowPrevented', mockCallback);

      // Set first allocation to 80000, then try to set second to 30000 (would exceed 100000)
      allocationManager.updateAllocation(0, 80000);
      allocationManager.updateAllocation(1, 30000); // This should be adjusted to 20000

      expect(mockCallback).toHaveBeenCalledWith({
        index: 1,
        attemptedValue: 30000,
        adjustedValue: 20000,
        maxAllowedValue: 20000,
      });

      const allocation = allocationManager.getAllocation(1);
      expect(allocation?.value).toBe(20000);
    });

    it('should calculate total allocated except specific item', () => {
      allocationManager.updateAllocation(0, 25000);
      allocationManager.updateAllocation(1, 35000);

      const totalExcept0 = allocationManager.getTotalAllocatedExcept(0);
      const totalExcept1 = allocationManager.getTotalAllocatedExcept(1);

      expect(totalExcept0).toBe(35000);
      expect(totalExcept1).toBe(25000);
    });

    it('should handle validation errors gracefully', () => {
      const mockCallback = jest.fn();
      allocationManager.on('validationFailed', mockCallback);

      // Try to set negative value
      allocationManager.updateAllocation(0, -1000);

      expect(mockCallback).toHaveBeenCalledWith({
        index: 0,
        attemptedValue: -1000,
        error: 'Value cannot be negative',
        currentValue: 0,
      });
    });
  });

  describe('Proportional Adjustment', () => {
    beforeEach(() => {
      allocationManager.initializeItems('[data-allocation-container]', '[data-allocation-item]');
      patrimonySync.updateValidationConfig({
        allowOverflow: true,
        proportionalAdjustment: true,
      });
    });

    it.skip('should apply proportional adjustment when overflow occurs', () => {
      const mockCallback = jest.fn();
      allocationManager.on('overflowAdjusted', mockCallback);

      // Set allocations that exceed 100% by using PatrimonySync directly
      patrimonySync.updateValidationConfig({
        allowOverflow: true,
        proportionalAdjustment: false,
      });

      patrimonySync.updateAllocation(0, 80000);
      patrimonySync.updateAllocation(1, 40000); // Total: 120000

      // Verify we have overflow
      expect(patrimonySync.getTotalAllocated()).toBe(120000);

      // Manually trigger proportional adjustment
      allocationManager.applyProportionalAdjustment();

      // Check that adjustment was applied
      expect(mockCallback).toHaveBeenCalled();
      expect(patrimonySync.getTotalAllocated()).toBeCloseTo(100000, 0);

      // Check that allocations were adjusted proportionally
      const allocation0 = patrimonySync.getAllocation(0);
      const allocation1 = patrimonySync.getAllocation(1);

      expect(allocation0?.value).toBeCloseTo(66666.67, 0); // 80000 * (100000/120000)
      expect(allocation1?.value).toBeCloseTo(33333.33, 0); // 40000 * (100000/120000)
    });

    it('should not apply adjustment if total is within limits', () => {
      const mockCallback = jest.fn();
      allocationManager.on('overflowAdjusted', mockCallback);

      allocationManager.updateAllocation(0, 40000);
      allocationManager.updateAllocation(1, 30000); // Total: 70000

      expect(mockCallback).not.toHaveBeenCalled();
    });
  });

  describe('Display Updates', () => {
    beforeEach(() => {
      allocationManager.initializeItems('[data-allocation-container]', '[data-allocation-item]');
    });

    it('should update input display when allocation changes', () => {
      allocationManager.updateAllocation(0, 25000);

      const input = allocationManager.getItemInput(0);
      expect(input?.value).toContain('25.000');
    });

    it('should update percentage display', () => {
      allocationManager.updateAllocation(0, 25000);

      const element = allocationManager.getItemElement(0);
      const percentageDisplay = element?.querySelector('[data-percentage-display]');
      expect(percentageDisplay?.textContent).toBe('25.0%');
    });

    it('should update value display', () => {
      allocationManager.updateAllocation(0, 25000);

      const element = allocationManager.getItemElement(0);
      const valueDisplay = element?.querySelector('[data-value-display]');
      expect(valueDisplay?.textContent).toContain('25.000');
    });

    it('should update visual state classes', () => {
      const element = allocationManager.getItemElement(0);

      // Normal state
      allocationManager.updateAllocation(0, 25000);
      expect(element?.classList.contains('allocation-normal')).toBe(true);

      // Warning state (exceeds maxAllowed)
      allocationManager.updateAllocation(0, 60000); // 60% > 50% maxAllowed
      expect(element?.classList.contains('allocation-warning')).toBe(true);
    });
  });

  describe('Event Handling', () => {
    beforeEach(() => {
      allocationManager.initializeItems('[data-allocation-container]', '[data-allocation-item]');
    });

    it('should handle input events', () => {
      const input = allocationManager.getItemInput(0);
      const mockCallback = jest.fn();
      allocationManager.on('itemUpdated', mockCallback);

      // Simulate input event
      if (input) {
        input.value = '25000';
        input.dispatchEvent(new Event('input'));
      }

      expect(mockCallback).toHaveBeenCalled();
    });

    it('should handle slider events', () => {
      const element = allocationManager.getItemElement(0);
      const slider = element?.querySelector('[data-allocation-slider]') as HTMLInputElement;
      const mockCallback = jest.fn();
      allocationManager.on('itemUpdated', mockCallback);

      // Simulate slider input event
      if (slider) {
        slider.value = '25';
        slider.dispatchEvent(new Event('input'));
      }

      expect(mockCallback).toHaveBeenCalled();
    });

    it('should respond to PatrimonySync events', () => {
      allocationManager.updateAllocation(0, 25000);

      // Change main value should update displays
      patrimonySync.setMainValue(200000);

      const allocation = allocationManager.getAllocation(0);
      expect(allocation?.percentage).toBe(12.5); // 25000 / 200000 * 100
    });
  });

  describe('Utility Methods', () => {
    beforeEach(() => {
      allocationManager.initializeItems('[data-allocation-container]', '[data-allocation-item]');
    });

    it('should reset all allocations', () => {
      const mockCallback = jest.fn();
      allocationManager.on('allocationsReset', mockCallback);

      allocationManager.updateAllocation(0, 25000);
      allocationManager.updateAllocation(1, 35000);

      allocationManager.resetAllocations();

      expect(mockCallback).toHaveBeenCalledWith({
        itemCount: 2,
      });

      expect(allocationManager.getAllocations()).toHaveLength(0);
    });

    it('should update validation configuration', () => {
      allocationManager.updateValidationConfig({
        maxPercentage: 120,
        allowOverflow: true,
      });

      const config = patrimonySync.getValidationConfig();
      expect(config.maxPercentage).toBe(120);
      expect(config.allowOverflow).toBe(true);
    });

    it('should destroy and clean up resources', () => {
      const mockCallback = jest.fn();
      allocationManager.on('destroyed', mockCallback);

      allocationManager.destroy();

      expect(mockCallback).toHaveBeenCalled();
      expect(allocationManager.isReady()).toBe(false);
    });
  });

  describe('Input Parsing', () => {
    beforeEach(() => {
      allocationManager.initializeItems('[data-allocation-container]', '[data-allocation-item]');
    });

    it('should parse currency formatted input', () => {
      const input = allocationManager.getItemInput(0);
      const mockCallback = jest.fn();
      allocationManager.on('itemUpdated', mockCallback);

      if (input) {
        input.value = 'R$ 25.000,50';
        input.dispatchEvent(new Event('input'));
      }

      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          value: 25000.5,
        })
      );
    });

    it('should handle invalid input gracefully', () => {
      const input = allocationManager.getItemInput(0);
      const mockCallback = jest.fn();
      allocationManager.on('itemUpdated', mockCallback);

      if (input) {
        input.value = 'invalid';
        input.dispatchEvent(new Event('input'));
      }

      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          value: 0,
        })
      );
    });
  });
});
