import { CurrencyFormatter } from '../../../src/modules/currency/CurrencyFormatter';

describe('CurrencyFormatter', () => {
  let formatter: CurrencyFormatter;

  beforeEach(() => {
    (CurrencyFormatter as any).instance = undefined;
    formatter = CurrencyFormatter.getInstance();
  });

  test('should format positive numbers correctly', () => {
    expect(formatter.formatValue(1234.56)).toBe('R$ 1.234,56');
    expect(formatter.formatValue(1000)).toBe('R$ 1.000,00');
    expect(formatter.formatValue(1)).toBe('R$ 1,00');
  });

  test('should parse formatted currency strings correctly', () => {
    expect(formatter.parseValue('R$ 1.234,56')).toBe(1234.56);
    expect(formatter.parseValue('R$ 1.000,00')).toBe(1000);
    expect(formatter.parseValue('R$ 1,00')).toBe(1);
  });

  test('should handle edge cases', () => {
    expect(formatter.formatValue(0)).toBe('R$ 0,00');
    expect(formatter.parseValue('')).toBe(0);
    expect(formatter.parseValue('invalid')).toBe(0);
  });

  test('should create currency objects', () => {
    const result = formatter.createCurrencyObject(1234.56);
    expect(result.value).toBe(1234.56);
    expect(result.formatted).toBe('R$ 1.234,56');
    expect(result.currency).toBe('BRL');
  });
});
