import { CacheManager } from '../../../src/modules/patrimony/CacheManager';
import { AllocationItem } from '../../../src/modules/patrimony/types';

// Mock localStorage
const localStorageMock = (() => {
  let store: { [key: string]: string } = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    key: (index: number) => {
      const keys = Object.keys(store);
      return keys[index] || null;
    },
    get length() {
      return Object.keys(store).length;
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('CacheManager', () => {
  let cacheManager: CacheManager;
  let mockAllocations: Map<number, AllocationItem>;

  beforeEach(() => {
    // Reset singleton instance
    (CacheManager as any).instance = undefined;
    cacheManager = CacheManager.getInstance();

    // Clear localStorage
    localStorage.clear();

    // Create mock allocations
    mockAllocations = new Map();
    mockAllocations.set(0, {
      index: 0,
      category: 'Stocks',
      subcategory: 'Tech',
      value: 25000,
      percentage: 25,
      maxAllowed: 50,
    });
    mockAllocations.set(1, {
      index: 1,
      category: 'Bonds',
      subcategory: 'Government',
      value: 35000,
      percentage: 35,
      maxAllowed: 40,
    });
  });

  afterEach(() => {
    cacheManager.removeAllListeners();
    localStorage.clear();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = CacheManager.getInstance();
      const instance2 = CacheManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Data Saving', () => {
    it('should save data to localStorage', async () => {
      const mockCallback = jest.fn();
      cacheManager.on('dataSaved', mockCallback);

      await cacheManager.saveData(100000, mockAllocations);

      expect(mockCallback).toHaveBeenCalledWith({
        mainValue: 100000,
        allocationCount: 2,
        timestamp: expect.any(Number),
      });

      // Check that data was actually saved
      const savedData = localStorage.getItem('patrimony_main_data');
      expect(savedData).toBeTruthy();

      const version = localStorage.getItem('patrimony_cache_version');
      expect(version).toBe('1.0.0');
    });

    it('should handle save errors gracefully', async () => {
      const mockCallback = jest.fn();
      cacheManager.on('error', mockCallback);

      // Mock localStorage to throw error
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = jest.fn(() => {
        throw new Error('Storage quota exceeded');
      });

      await expect(cacheManager.saveData(100000, mockAllocations)).rejects.toThrow();
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Failed to save data to cache',
        })
      );

      // Restore original method
      localStorage.setItem = originalSetItem;
    });
  });

  describe('Data Loading', () => {
    it('should load data from localStorage', async () => {
      const mockCallback = jest.fn();
      cacheManager.on('dataLoaded', mockCallback);

      // First save data
      await cacheManager.saveData(100000, mockAllocations);

      // Then load it
      const loadedData = await cacheManager.loadData();

      expect(loadedData).toBeTruthy();
      expect(loadedData?.mainValue).toBe(100000);
      expect(loadedData?.allocations.size).toBe(2);
      expect(loadedData?.version).toBe('1.0.0');

      expect(mockCallback).toHaveBeenCalledWith({
        mainValue: 100000,
        allocationCount: 2,
        timestamp: expect.any(Number),
        age: expect.any(Number),
      });
    });

    it('should return null when no data exists', async () => {
      const loadedData = await cacheManager.loadData();
      expect(loadedData).toBeNull();
    });

    it('should handle corrupted data gracefully', async () => {
      const mockCallback = jest.fn();
      cacheManager.on('error', mockCallback);

      // Set corrupted data
      localStorage.setItem('patrimony_main_data', 'invalid json');
      localStorage.setItem('patrimony_cache_version', '1.0.0');

      const loadedData = await cacheManager.loadData();
      expect(loadedData).toBeNull();
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Failed to load data from cache',
        })
      );
    });

    it('should clear expired cache', async () => {
      // Save data
      await cacheManager.saveData(100000, mockAllocations);

      // Mock expired timestamp
      const savedData = localStorage.getItem('patrimony_main_data');
      if (savedData) {
        const parsed = JSON.parse(savedData);
        parsed.timestamp = Date.now() - 31 * 24 * 60 * 60 * 1000; // 31 days ago
        localStorage.setItem('patrimony_main_data', JSON.stringify(parsed));
      }

      const loadedData = await cacheManager.loadData();
      expect(loadedData).toBeNull();

      // Cache should be cleared
      expect(localStorage.getItem('patrimony_main_data')).toBeNull();
    });
  });

  describe('Cache Management', () => {
    it('should clear all cache data', () => {
      const mockCallback = jest.fn();
      cacheManager.on('cacheCleared', mockCallback);

      // Set some data
      localStorage.setItem('patrimony_main_data', 'test');
      localStorage.setItem('patrimony_cache_version', '1.0.0');
      localStorage.setItem('patrimony_other_data', 'test');

      cacheManager.clearCache();

      expect(localStorage.getItem('patrimony_main_data')).toBeNull();
      expect(localStorage.getItem('patrimony_cache_version')).toBeNull();
      expect(mockCallback).toHaveBeenCalledWith({
        keysRemoved: expect.any(Number),
      });
    });

    it('should get cache statistics', async () => {
      // No data initially
      let stats = cacheManager.getCacheStats();
      expect(stats.hasData).toBe(false);

      // Save data
      await cacheManager.saveData(100000, mockAllocations);

      // Check stats with data
      stats = cacheManager.getCacheStats();
      expect(stats.hasData).toBe(true);
      expect(stats.version).toBe('1.0.0');
      expect(stats.age).toBeGreaterThanOrEqual(0);
      expect(stats.size).toBeGreaterThan(0);
      expect(stats.isExpired).toBe(false);
    });

    it('should handle stats errors gracefully', () => {
      // Set corrupted data
      localStorage.setItem('patrimony_main_data', 'invalid json');

      const stats = cacheManager.getCacheStats();
      expect(stats.hasData).toBe(false);
      expect(stats.isExpired).toBe(true);
    });
  });

  describe('Data Migration', () => {
    it('should migrate data from older version', async () => {
      const mockCallback = jest.fn();
      cacheManager.on('migrationCompleted', mockCallback);

      // Set old version data
      const oldData = {
        mainValue: 50000,
        allocations: [
          { category: 'Stocks', value: 20000 },
          { category: 'Bonds', value: 15000 },
        ],
      };

      localStorage.setItem('patrimony_main_data', JSON.stringify(oldData));
      localStorage.setItem('patrimony_cache_version', '0.1.0');

      const loadedData = await cacheManager.loadData();

      expect(loadedData).toBeTruthy();
      expect(loadedData?.mainValue).toBe(50000);
      expect(mockCallback).toHaveBeenCalledWith({
        fromVersion: '0.1.0',
        toVersion: '1.0.0',
        allocationCount: expect.any(Number),
      });
    });

    it('should handle migration failure', async () => {
      // Set unknown version
      localStorage.setItem('patrimony_main_data', '{"test": "data"}');
      localStorage.setItem('patrimony_cache_version', '999.0.0');

      const loadedData = await cacheManager.loadData();
      expect(loadedData).toBeNull();
    });
  });

  describe('Import/Export', () => {
    it('should export data', async () => {
      await cacheManager.saveData(100000, mockAllocations);

      const exportedData = await cacheManager.exportData();
      expect(exportedData).toBeTruthy();

      const parsed = JSON.parse(exportedData!);
      expect(parsed.mainValue).toBe(100000);
      expect(parsed.exportedAt).toBeTruthy();
      expect(parsed.exportVersion).toBe('1.0.0');
    });

    it('should return null when exporting empty cache', async () => {
      const exportedData = await cacheManager.exportData();
      expect(exportedData).toBeNull();
    });

    it('should import data', async () => {
      const mockCallback = jest.fn();
      cacheManager.on('dataImported', mockCallback);

      const importData = {
        mainValue: 75000,
        allocations: [
          [
            0,
            {
              index: 0,
              category: 'Stocks',
              subcategory: 'Tech',
              value: 30000,
              percentage: 40,
              maxAllowed: 50,
            },
          ],
        ],
        timestamp: Date.now(),
        version: '1.0.0',
      };

      const success = await cacheManager.importData(JSON.stringify(importData));
      expect(success).toBe(true);
      expect(mockCallback).toHaveBeenCalledWith({
        mainValue: 75000,
        allocationCount: 1,
        importedAt: expect.any(Number),
      });

      // Verify data was saved
      const loadedData = await cacheManager.loadData();
      expect(loadedData?.mainValue).toBe(75000);
    });

    it('should handle invalid import data', async () => {
      const mockCallback = jest.fn();
      cacheManager.on('error', mockCallback);

      const success = await cacheManager.importData('invalid json');
      expect(success).toBe(false);
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Failed to import data',
        })
      );
    });

    it('should handle import data with missing fields', async () => {
      const invalidData = { someField: 'value' };

      const success = await cacheManager.importData(JSON.stringify(invalidData));
      expect(success).toBe(false);
    });
  });

  describe('Configuration', () => {
    it('should set cache max age', () => {
      cacheManager.setCacheMaxAge(60000); // 1 minute

      // This is a bit tricky to test since MAX_CACHE_AGE is private
      // We can test indirectly by checking if cache expires sooner
      expect(() => cacheManager.setCacheMaxAge(60000)).not.toThrow();
    });

    it('should get current version', () => {
      const version = cacheManager.getCurrentVersion();
      expect(version).toBe('1.0.0');
    });

    it('should ignore invalid max age', () => {
      expect(() => cacheManager.setCacheMaxAge(-1000)).not.toThrow();
      expect(() => cacheManager.setCacheMaxAge(0)).not.toThrow();
    });
  });

  describe('Storage Availability', () => {
    it('should handle localStorage unavailability', async () => {
      // Mock localStorage to be unavailable
      const originalGetItem = localStorage.getItem;
      localStorage.getItem = jest.fn(() => {
        throw new Error('localStorage not available');
      });

      const loadedData = await cacheManager.loadData();
      expect(loadedData).toBeNull();

      // Restore
      localStorage.getItem = originalGetItem;
    });

    it('should handle quota exceeded error', async () => {
      const mockCallback = jest.fn();
      cacheManager.on('error', mockCallback);

      // Mock localStorage to throw quota error
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = jest.fn((key: string) => {
        if (key === '__quota_test__') {
          throw new Error('Quota exceeded');
        }
      });

      await expect(cacheManager.saveData(100000, mockAllocations)).rejects.toThrow();

      // Restore
      localStorage.setItem = originalSetItem;
    });
  });

  describe('Serialization', () => {
    it('should serialize and deserialize data correctly', async () => {
      await cacheManager.saveData(100000, mockAllocations);
      const loadedData = await cacheManager.loadData();

      expect(loadedData?.mainValue).toBe(100000);
      expect(loadedData?.allocations).toBeInstanceOf(Map);
      expect(loadedData?.allocations.size).toBe(2);

      const allocation0 = loadedData?.allocations.get(0);
      expect(allocation0?.category).toBe('Stocks');
      expect(allocation0?.value).toBe(25000);
    });
  });
});
