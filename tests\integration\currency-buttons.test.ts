import { CurrencyButtons } from '../../src/modules/currency/CurrencyButtons';
import { CurrencyController } from '../../src/modules/currency/CurrencyController';

describe('Currency Buttons Integration', () => {
  let controller: CurrencyController;
  let buttons: CurrencyButtons;
  let mockInput: HTMLInputElement;
  let mockIncrement: HTMLButtonElement;
  let mockDecrement: HTMLButtonElement;
  let mockContainer: HTMLElement;

  beforeEach(() => {
    controller = new CurrencyController();
    buttons = new CurrencyButtons(controller);

    // Create mock elements
    mockInput = {
      type: 'text',
      value: '',
      dataset: {},
      addEventListener: jest.fn(),
      classList: {
        add: jest.fn(),
        remove: jest.fn(),
        toggle: jest.fn(),
      },
      setSelectionRange: jest.fn(),
      select: jest.fn(),
      selectionStart: 0,
      focus: jest.fn(),
      parentElement: null,
    } as any;

    mockIncrement = {
      type: 'button',
      disabled: false,
      addEventListener: jest.fn(),
      classList: {
        add: jest.fn(),
        remove: jest.fn(),
        toggle: jest.fn(),
      },
      setAttribute: jest.fn(),
    } as any;

    mockDecrement = {
      type: 'button',
      disabled: false,
      addEventListener: jest.fn(),
      classList: {
        add: jest.fn(),
        remove: jest.fn(),
        toggle: jest.fn(),
      },
      setAttribute: jest.fn(),
    } as any;

    mockContainer = {
      querySelector: jest.fn((selector) => {
        if (selector === '[data-currency-increment]') return mockIncrement;
        if (selector === '[data-currency-decrement]') return mockDecrement;
        return null;
      }),
    } as any;

    // Set up parent relationship
    Object.defineProperty(mockInput, 'parentElement', {
      value: mockContainer,
      configurable: true,
    });
  });

  test('should setup buttons correctly', () => {
    controller.initializeInput(mockInput);
    buttons.setupButtons(mockInput);

    expect(mockIncrement.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
    expect(mockIncrement.classList.add).toHaveBeenCalledWith(
      'currency-btn',
      'currency-btn--increment'
    );
  });

  test('should handle missing buttons gracefully', () => {
    mockContainer.querySelector = jest.fn(() => null);

    expect(() => {
      buttons.setupButtons(mockInput);
    }).not.toThrow();
  });

  test('should get button pair for input', () => {
    controller.initializeInput(mockInput);
    buttons.setupButtons(mockInput);

    const buttonPair = buttons.getButtonPair(mockInput);
    expect(buttonPair).toBeDefined();
    expect(buttonPair?.increment).toBe(mockIncrement);
  });

  test('should remove buttons correctly', () => {
    controller.initializeInput(mockInput);
    buttons.setupButtons(mockInput);

    buttons.removeButtons(mockInput);

    expect(buttons.getButtonPair(mockInput)).toBeUndefined();
    expect(mockIncrement.classList.remove).toHaveBeenCalledWith(
      'currency-btn',
      'currency-btn--increment'
    );
  });
});
