# Requirements Document

## Introduction

Este projeto visa modularizar o código JavaScript existente de um site Webflow exportado, transformando-o em uma estrutura TypeScript organizada e reutilizável. O site atual contém funcionalidades complexas de calculadora de patrimônio, alocação de ativos, animações interativas e integração com OpenAI, tudo implementado em JavaScript inline. O objetivo é manter toda a funcionalidade existente enquanto organiza o código em módulos TypeScript bem estruturados.

## Requirements

### Requirement 1

**User Story:** Como desenvolvedor, quero que todo o código JavaScript inline seja extraído e modularizado em TypeScript, para que eu possa manter e reutilizar o código de forma mais eficiente.

#### Acceptance Criteria

1. WHEN o sistema for inicializado THEN todos os scripts inline devem ser substituídos por módulos TypeScript
2. WHEN um módulo for carregado THEN ele deve manter exatamente a mesma funcionalidade do código original
3. WHEN o código for refatorado THEN não deve haver quebra de funcionalidade existente
4. WHEN os módulos forem criados THEN eles devem seguir padrões TypeScript modernos

### Requirement 2

**User Story:** Como desenvolvedor, quero um sistema de formatação de moeda modularizado, para que eu possa reutilizar a funcionalidade de formatação em diferentes partes da aplicação.

#### Acceptance Criteria

1. WHEN um valor monetário for inserido THEN o sistema deve formatar automaticamente para o padrão brasileiro (R$)
2. WHEN o usuário interagir com controles de incremento/decremento THEN os valores devem ser atualizados com formatação correta
3. WHEN um evento de mudança de moeda for disparado THEN outros componentes devem ser notificados
4. WHEN o sistema for inicializado THEN deve suportar múltiplos inputs de moeda simultaneamente

### Requirement 3

**User Story:** Como desenvolvedor, quero um sistema de sincronização de patrimônio modularizado, para que eu possa gerenciar a alocação de ativos de forma consistente.

#### Acceptance Criteria

1. WHEN o valor principal do patrimônio for alterado THEN todas as alocações devem ser recalculadas automaticamente
2. WHEN uma alocação individual for modificada THEN o sistema deve validar se não excede o total disponível
3. WHEN o total das alocações exceder 100% THEN o sistema deve aplicar ajustes proporcionais
4. WHEN os dados forem alterados THEN eles devem ser persistidos no localStorage
5. WHEN a página for recarregada THEN os valores salvos devem ser restaurados

### Requirement 4

**User Story:** Como desenvolvedor, quero um sistema de animações interativas modularizado, para que eu possa aplicar efeitos visuais consistentes aos elementos da interface.

#### Acceptance Criteria

1. WHEN o usuário interagir com botões THEN animações de hover e press devem ser aplicadas
2. WHEN elementos forem ativados/desativados THEN transições suaves devem ser executadas
3. WHEN sliders forem manipulados THEN feedback visual deve ser fornecido
4. WHEN itens de produto forem interagidos THEN estados ativos/inativos devem ser animados

### Requirement 5

**User Story:** Como desenvolvedor, quero um sistema de controle de visibilidade de seções modularizado, para que eu possa gerenciar quando componentes flutuantes aparecem na tela.

#### Acceptance Criteria

1. WHEN uma seção específica estiver visível THEN o componente flutuante deve aparecer com animação
2. WHEN a seção sair de vista THEN o componente deve desaparecer suavemente
3. WHEN o sistema for inicializado THEN deve usar Intersection Observer para detecção de visibilidade
4. WHEN animações estiverem em progresso THEN não deve permitir sobreposição de animações

### Requirement 6

**User Story:** Como desenvolvedor, quero um sistema de integração com OpenAI modularizado, para que eu possa processar solicitações de alocação em linguagem natural.

#### Acceptance Criteria

1. WHEN o usuário inserir uma solicitação em texto THEN o sistema deve processar via OpenAI API
2. WHEN uma resposta for recebida THEN deve ser parseada e aplicada às alocações
3. WHEN erros ocorrerem THEN feedback visual deve ser fornecido ao usuário
4. WHEN a API key não estiver configurada THEN deve solicitar configuração via modal
5. WHEN alocações forem aplicadas THEN devem respeitar as validações do sistema de patrimônio

### Requirement 7

**User Story:** Como desenvolvedor, quero um sistema de gerenciamento de produtos/ativos modularizado, para que eu possa controlar a interação com itens de alocação.

#### Acceptance Criteria

1. WHEN o usuário interagir com um item THEN ele deve alternar entre estados ativo/inativo
2. WHEN um slider for arrastado THEN o item deve permanecer ativo durante a interação
3. WHEN o usuário "pinar" um item THEN ele deve permanecer ativo até ser "despinado"
4. WHEN múltiplos itens estiverem sendo manipulados THEN apenas um deve estar ativo por vez

### Requirement 8

**User Story:** Como desenvolvedor, quero um sistema de gráficos animados modularizado, para que eu possa exibir resultados de alocação de forma visual.

#### Acceptance Criteria

1. WHEN dados de alocação forem fornecidos THEN gráficos de barras devem ser renderizados
2. WHEN valores mudarem THEN as barras devem animar para os novos valores
3. WHEN a animação for iniciada THEN deve usar GSAP para transições suaves
4. WHEN múltiplas categorias existirem THEN cada uma deve ter sua própria barra animada

### Requirement 9

**User Story:** Como desenvolvedor, quero um sistema de tipos TypeScript bem definido, para que eu possa ter type safety e melhor experiência de desenvolvimento.

#### Acceptance Criteria

1. WHEN interfaces forem criadas THEN devem representar todas as estruturas de dados usadas
2. WHEN funções forem definidas THEN devem ter tipos de parâmetros e retorno explícitos
3. WHEN eventos customizados forem usados THEN devem ter tipos definidos para seus payloads
4. WHEN configurações forem definidas THEN devem ter tipos para validação

### Requirement 10

**User Story:** Como desenvolvedor, quero documentação completa dos módulos, para que eu possa entender e manter o código facilmente.

#### Acceptance Criteria

1. WHEN cada módulo for criado THEN deve ter documentação JSDoc completa
2. WHEN funções públicas existirem THEN devem ter exemplos de uso
3. WHEN interfaces forem definidas THEN devem ter descrições de cada propriedade
4. WHEN o sistema for complexo THEN deve ter README explicativo
