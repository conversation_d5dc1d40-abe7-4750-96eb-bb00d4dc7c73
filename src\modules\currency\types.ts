// Currency module types
export interface CurrencyValue {
  value: number;
  formatted: string;
  currency: string;
}

export interface CurrencyInputConfig {
  isMain?: boolean;
  allowNegative?: boolean;
  maxValue?: number;
  minValue?: number;
  onValueChange?: (value: CurrencyValue) => void;
}

export interface CurrencyChangeEvent {
  value: number;
  formatted: string;
  input: HTMLInputElement;
}

export interface CurrencyFormatterOptions {
  locale?: string;
  currency?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}
