/**
 * General utility functions for the application
 * Provides common helper methods for data manipulation, validation, and formatting
 */
export class Utils {
  /**
   * Generate a unique ID
   * @param prefix - Optional prefix for the ID
   * @returns Unique identifier string
   */
  static generateId(prefix: string = 'id'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Deep clone an object
   * @param obj - Object to clone
   * @returns Deep cloned object
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T;
    }

    if (obj instanceof Array) {
      return obj.map((item) => this.deepClone(item)) as unknown as T;
    }

    if (typeof obj === 'object') {
      const cloned = {} as T;
      Object.keys(obj).forEach((key) => {
        (cloned as any)[key] = this.deepClone((obj as any)[key]);
      });
      return cloned;
    }

    return obj;
  }

  /**
   * Check if value is empty (null, undefined, empty string, empty array, empty object)
   * @param value - Value to check
   * @returns True if value is empty
   */
  static isEmpty(value: any): boolean {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim().length === 0;
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  }

  /**
   * Clamp a number between min and max values
   * @param value - Value to clamp
   * @param min - Minimum value
   * @param max - Maximum value
   * @returns Clamped value
   */
  static clamp(value: number, min: number, max: number): number {
    return Math.min(Math.max(value, min), max);
  }

  /**
   * Linear interpolation between two values
   * @param start - Start value
   * @param end - End value
   * @param factor - Interpolation factor (0-1)
   * @returns Interpolated value
   */
  static lerp(start: number, end: number, factor: number): number {
    return start + (end - start) * this.clamp(factor, 0, 1);
  }

  /**
   * Map a value from one range to another
   * @param value - Value to map
   * @param inMin - Input range minimum
   * @param inMax - Input range maximum
   * @param outMin - Output range minimum
   * @param outMax - Output range maximum
   * @returns Mapped value
   */
  static mapRange(
    value: number,
    inMin: number,
    inMax: number,
    outMin: number,
    outMax: number
  ): number {
    return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
  }

  /**
   * Round number to specified decimal places
   * @param value - Number to round
   * @param decimals - Number of decimal places
   * @returns Rounded number
   */
  static roundTo(value: number, decimals: number): number {
    const factor = Math.pow(10, decimals);
    return Math.round(value * factor) / factor;
  }

  /**
   * Format number with thousands separators
   * @param value - Number to format
   * @param locale - Locale for formatting (defaults to pt-BR)
   * @returns Formatted number string
   */
  static formatNumber(value: number, locale: string = 'pt-BR'): string {
    return new Intl.NumberFormat(locale).format(value);
  }

  /**
   * Parse number from formatted string
   * @param value - Formatted number string
   * @param locale - Locale for parsing (defaults to pt-BR)
   * @returns Parsed number
   */
  static parseNumber(value: string, locale: string = 'pt-BR'): number {
    // Remove currency symbols and spaces
    const cleaned = value.replace(/[^\d,.-]/g, '');

    // Handle Brazilian format (1.234,56)
    if (locale === 'pt-BR') {
      return parseFloat(cleaned.replace(/\./g, '').replace(',', '.')) || 0;
    }

    // Handle US format (1,234.56)
    return parseFloat(cleaned.replace(/,/g, '')) || 0;
  }

  /**
   * Validate email format
   * @param email - Email string to validate
   * @returns True if email is valid
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Sanitize string for HTML output
   * @param str - String to sanitize
   * @returns Sanitized string
   */
  static sanitizeHtml(str: string): string {
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
  }

  /**
   * Capitalize first letter of string
   * @param str - String to capitalize
   * @returns Capitalized string
   */
  static capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  /**
   * Convert string to kebab-case
   * @param str - String to convert
   * @returns Kebab-case string
   */
  static toKebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/[\s_]+/g, '-')
      .toLowerCase();
  }

  /**
   * Convert string to camelCase
   * @param str - String to convert
   * @returns CamelCase string
   */
  static toCamelCase(str: string): string {
    return str
      .replace(/[-_\s]+(.)?/g, (_, char) => (char ? char.toUpperCase() : ''))
      .replace(/^[A-Z]/, (char) => char.toLowerCase());
  }

  /**
   * Wait for specified time
   * @param ms - Milliseconds to wait
   * @returns Promise that resolves after delay
   */
  static wait(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Retry function with exponential backoff
   * @param fn - Function to retry
   * @param maxAttempts - Maximum number of attempts
   * @param baseDelay - Base delay in milliseconds
   * @returns Promise with function result
   */
  static async retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxAttempts) {
          throw lastError;
        }

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await this.wait(delay);
      }
    }

    throw lastError!;
  }

  /**
   * Create a promise that rejects after timeout
   * @param promise - Promise to wrap
   * @param timeoutMs - Timeout in milliseconds
   * @param timeoutMessage - Error message for timeout
   * @returns Promise that rejects on timeout
   */
  static withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    timeoutMessage: string = 'Operation timed out'
  ): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * Group array items by key
   * @param array - Array to group
   * @param keyFn - Function to extract grouping key
   * @returns Grouped object
   */
  static groupBy<T, K extends string | number>(array: T[], keyFn: (item: T) => K): Record<K, T[]> {
    return array.reduce(
      (groups, item) => {
        const key = keyFn(item);
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(item);
        return groups;
      },
      {} as Record<K, T[]>
    );
  }

  /**
   * Remove duplicates from array
   * @param array - Array to deduplicate
   * @param keyFn - Optional function to extract comparison key
   * @returns Array without duplicates
   */
  static unique<T>(array: T[], keyFn?: (item: T) => any): T[] {
    if (!keyFn) {
      return [...new Set(array)];
    }

    const seen = new Set();
    return array.filter((item) => {
      const key = keyFn(item);
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }
}
