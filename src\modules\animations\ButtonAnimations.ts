import { ANIMATION_VARIANTS, MotionController } from './MotionController';
import type { AnimationVariant } from './types';

/**
 * Button animations using native Framer Motion
 * Much simpler than before - just returns variants!
 */
export class ButtonAnimations {
  private motionController: MotionController;

  constructor() {
    this.motionController = MotionController.getInstance();
  }

  /**
   * Get currency button animation variants
   */
  getCurrencyButtonVariants(): AnimationVariant {
    return ANIMATION_VARIANTS.currencyButton;
  }

  /**
   * Get hero button animation variants
   */
  getHeroButtonVariants(): AnimationVariant {
    return ANIMATION_VARIANTS.heroButton;
  }

  /**
   * Get standard button hover variants
   */
  getButtonHoverVariants(): AnimationVariant {
    return ANIMATION_VARIANTS.buttonHover;
  }

  /**
   * Apply animations to existing buttons in DOM
   * PRESERVES Webflow animations (data-w-id) - only adds CSS enhancements
   */
  refreshButtonAnimations(): void {
    // Currency control buttons - only add CSS classes, don't interfere with functionality
    const currencyButtons = document.querySelectorAll('[currency-control]');
    currencyButtons.forEach((button) => {
      // Only add CSS classes for styling, don't override Webflow behavior
      button.classList.add('enhanced-currency-btn');
    });

    // Hero buttons - preserve Webflow animations (data-w-id)
    const heroButtons = document.querySelectorAll('.button-hero');
    heroButtons.forEach((button) => {
      // Check if button has Webflow animation (data-w-id)
      if (button.hasAttribute('data-w-id')) {
        // Don't interfere with Webflow animations - only add complementary styles
        button.classList.add('enhanced-hero-btn-with-webflow');
      } else {
        // No Webflow animation, safe to add our own
        button.classList.add('enhanced-hero-btn');
      }
    });

    // Standard buttons - only add minimal enhancements
    const standardButtons = document.querySelectorAll(
      'button:not([currency-control]):not(.button-hero)'
    );
    standardButtons.forEach((button) => {
      // Only add CSS class, don't interfere with any existing functionality
      button.classList.add('enhanced-standard-btn');
    });
  }

  /**
   * Get all available button variants
   */
  getAllButtonVariants(): Record<string, AnimationVariant> {
    return {
      currencyButton: this.getCurrencyButtonVariants(),
      heroButton: this.getHeroButtonVariants(),
      buttonHover: this.getButtonHoverVariants(),
    };
  }
}
