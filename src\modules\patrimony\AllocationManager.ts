import { EventEmitter } from '../core/EventEmitter';
import { PatrimonySync } from './PatrimonySync';
import {
  AllocationChangeEvent,
  AllocationItem,
  AllocationValidationConfig,
  ValidationResult,
} from './types';

/**
 * AllocationManager - Manages coordination between allocation items and PatrimonySync
 *
 * This class handles the initialization of allocation items, coordinates their interactions
 * with the PatrimonySync singleton, and provides real-time synchronization between items.
 *
 * Events emitted:
 * - 'itemInitialized': When an allocation item is initialized
 * - 'itemUpdated': When an allocation item is updated
 * - 'validationFailed': When validation fails for an item
 * - 'overflowAdjusted': When proportional adjustment is applied
 * - 'syncCompleted': When synchronization between items is completed
 */
export class AllocationManager extends EventEmitter {
  private patrimonySync: PatrimonySync;
  private items: Map<number, HTMLElement> = new Map();
  private inputs: Map<number, HTMLInputElement> = new Map();
  private sliders: Map<number, HTMLElement> = new Map();
  private isInitialized: boolean = false;
  private validationConfig: AllocationValidationConfig;

  constructor(patrimonySync?: PatrimonySync) {
    super();
    this.patrimonySync = patrimonySync || PatrimonySync.getInstance();
    this.validationConfig = this.patrimonySync.getValidationConfig();
    this.setupEventListeners();
  }

  /**
   * Initialize allocation items from DOM elements
   * @param containerSelector CSS selector for the container holding allocation items
   * @param itemSelector CSS selector for individual allocation items
   */
  initializeItems(
    containerSelector: string = '[data-allocation-container]',
    itemSelector: string = '[data-allocation-item]'
  ): void {
    const container = document.querySelector(containerSelector);
    if (!container) {
      throw new Error(`Container not found: ${containerSelector}`);
    }

    const itemElements = container.querySelectorAll(itemSelector);
    if (itemElements.length === 0) {
      console.warn(`No allocation items found with selector: ${itemSelector}`);
      return;
    }

    itemElements.forEach((element, index) => {
      this.initializeItem(element as HTMLElement, index);
    });

    this.isInitialized = true;
    this.emit('initializationCompleted', {
      itemCount: this.items.size,
      items: Array.from(this.items.keys()),
    });
  }

  /**
   * Initialize a single allocation item
   * @param element HTML element representing the allocation item
   * @param index Index of the allocation item
   */
  private initializeItem(element: HTMLElement, index: number): void {
    // Extract item data from DOM attributes
    const category = element.dataset.category || 'Unknown';
    const subcategory = element.dataset.subcategory || 'Unknown';
    const maxAllowed = parseFloat(element.dataset.maxAllowed || '100');
    const initialValue = parseFloat(element.dataset.initialValue || '0');

    // Find input and slider elements
    const input = element.querySelector('[data-allocation-input]') as HTMLInputElement;
    const slider = element.querySelector('[data-allocation-slider]') as HTMLElement;

    if (!input) {
      console.warn(`No input found for allocation item ${index}`);
      return;
    }

    // Store references
    this.items.set(index, element);
    this.inputs.set(index, input);
    if (slider) {
      this.sliders.set(index, slider);
    }

    // Create allocation item in PatrimonySync
    const allocation: AllocationItem = {
      index,
      category,
      subcategory,
      value: initialValue,
      percentage: 0,
      maxAllowed,
    };

    this.patrimonySync.addAllocation(allocation);

    // Setup event listeners for this item
    this.setupItemEvents(index, input, slider);

    // Set initial value if provided
    if (initialValue > 0) {
      this.updateAllocation(index, initialValue);
    }

    this.emit('itemInitialized', {
      index,
      category,
      subcategory,
      maxAllowed,
      initialValue,
      element,
    });
  }

  /**
   * Setup event listeners for an allocation item
   * @param index Item index
   * @param input Input element
   * @param slider Slider element (optional)
   */
  private setupItemEvents(index: number, input: HTMLInputElement, slider?: HTMLElement): void {
    // Input change event
    input.addEventListener('input', (event) => {
      const target = event.target as HTMLInputElement;
      const value = this.parseInputValue(target.value);
      this.updateAllocation(index, value);
    });

    input.addEventListener('blur', (event) => {
      const target = event.target as HTMLInputElement;
      const value = this.parseInputValue(target.value);
      this.validateAndUpdateAllocation(index, value);
    });

    // Slider events if available
    if (slider) {
      slider.addEventListener('input', (event) => {
        const target = event.target as HTMLInputElement;
        const percentage = parseFloat(target.value);
        const mainValue = this.patrimonySync.getMainValue();
        const value = (percentage / 100) * mainValue;
        this.updateAllocation(index, value);
      });

      slider.addEventListener('change', (event) => {
        const target = event.target as HTMLInputElement;
        const percentage = parseFloat(target.value);
        const mainValue = this.patrimonySync.getMainValue();
        const value = (percentage / 100) * mainValue;
        this.validateAndUpdateAllocation(index, value);
      });
    }
  }

  /**
   * Update an allocation with validation and overflow prevention
   * @param index Allocation index
   * @param value New value
   */
  updateAllocation(index: number, value: number): void {
    // Pre-validation checks
    if (value < 0) {
      this.handleValidationError(index, value, new Error('Value cannot be negative'));
      return;
    }

    const mainValue = this.patrimonySync.getMainValue();
    if (mainValue === 0) {
      this.handleValidationError(index, value, new Error('Main patrimony value must be set first'));
      return;
    }

    // Check if this allocation would cause overflow
    const currentAllocation = this.patrimonySync.getAllocation(index);
    if (!currentAllocation) {
      this.handleValidationError(index, value, new Error('Allocation not found'));
      return;
    }

    const otherAllocationsTotal = this.getTotalAllocatedExcept(index);
    const totalWithNewValue = otherAllocationsTotal + value;

    if (totalWithNewValue > mainValue && !this.validationConfig.allowOverflow) {
      const maxAllowedValue = mainValue - otherAllocationsTotal;
      this.handleOverflowPrevention(index, value, maxAllowedValue);
      return;
    }

    try {
      this.patrimonySync.updateAllocation(index, value);
      this.updateItemDisplay(index);

      this.emit('itemUpdated', {
        index,
        value,
        allocation: this.patrimonySync.getAllocation(index),
      });
    } catch (error) {
      this.handleValidationError(index, value, error as Error);
    }
  }

  /**
   * Validate and update allocation with comprehensive checks
   * @param index Allocation index
   * @param value New value
   */
  private validateAndUpdateAllocation(index: number, value: number): void {
    // Pre-validation checks
    if (value < 0) {
      this.handleValidationError(index, value, new Error('Value cannot be negative'));
      return;
    }

    const mainValue = this.patrimonySync.getMainValue();
    if (mainValue === 0) {
      this.handleValidationError(index, value, new Error('Main patrimony value must be set first'));
      return;
    }

    // Check if this allocation would cause overflow
    const currentAllocation = this.patrimonySync.getAllocation(index);
    if (!currentAllocation) {
      this.handleValidationError(index, value, new Error('Allocation not found'));
      return;
    }

    const otherAllocationsTotal = this.getTotalAllocatedExcept(index);
    const totalWithNewValue = otherAllocationsTotal + value;

    if (totalWithNewValue > mainValue && !this.validationConfig.allowOverflow) {
      const maxAllowedValue = mainValue - otherAllocationsTotal;
      this.handleOverflowPrevention(index, value, maxAllowedValue);
      return;
    }

    // Update if validation passes
    this.updateAllocation(index, value);
  }

  /**
   * Handle overflow prevention by adjusting the value
   * @param index Allocation index
   * @param attemptedValue The value that would cause overflow
   * @param maxAllowedValue Maximum allowed value
   */
  private handleOverflowPrevention(
    index: number,
    attemptedValue: number,
    maxAllowedValue: number
  ): void {
    // Adjust to maximum allowed value
    const adjustedValue = Math.max(0, maxAllowedValue);

    this.updateAllocation(index, adjustedValue);

    this.emit('overflowPrevented', {
      index,
      attemptedValue,
      adjustedValue,
      maxAllowedValue,
    });

    // Update input display to show adjusted value
    this.updateItemDisplay(index);
  }

  /**
   * Apply proportional adjustment to all allocations
   * This is called when the total exceeds 100% and proportional adjustment is enabled
   */
  applyProportionalAdjustment(): void {
    const totalAllocated = this.patrimonySync.getTotalAllocated();
    const mainValue = this.patrimonySync.getMainValue();

    if (totalAllocated <= mainValue) {
      return; // No adjustment needed
    }

    const adjustmentFactor = mainValue / totalAllocated;
    const adjustedAllocations: Array<{ index: number; oldValue: number; newValue: number }> = [];

    // Apply proportional reduction to all allocations
    this.items.forEach((element, index) => {
      const allocation = this.patrimonySync.getAllocation(index);
      if (allocation) {
        const oldValue = allocation.value;
        const newValue = oldValue * adjustmentFactor;

        this.patrimonySync.updateAllocation(index, newValue);
        this.updateItemDisplay(index);

        adjustedAllocations.push({ index, oldValue, newValue });
      }
    });

    this.emit('overflowAdjusted', {
      adjustmentFactor,
      adjustedAllocations,
      totalBefore: totalAllocated,
      totalAfter: this.patrimonySync.getTotalAllocated(),
    });
  }

  /**
   * Update the display of an allocation item
   * @param index Allocation index
   */
  private updateItemDisplay(index: number): void {
    const allocation = this.patrimonySync.getAllocation(index);
    const input = this.inputs.get(index);
    const slider = this.sliders.get(index);
    const element = this.items.get(index);

    if (!allocation || !input || !element) {
      return;
    }

    // Update input value
    input.value = this.formatCurrency(allocation.value);

    // Update slider if available
    if (slider && slider instanceof HTMLInputElement) {
      slider.value = allocation.percentage.toString();
    }

    // Update percentage display
    const percentageDisplay = element.querySelector('[data-percentage-display]');
    if (percentageDisplay) {
      percentageDisplay.textContent = `${allocation.percentage.toFixed(1)}%`;
    }

    // Update value display
    const valueDisplay = element.querySelector('[data-value-display]');
    if (valueDisplay) {
      valueDisplay.textContent = this.formatCurrency(allocation.value);
    }

    // Update visual state based on allocation status
    this.updateItemVisualState(index, allocation);
  }

  /**
   * Update visual state of an allocation item
   * @param index Allocation index
   * @param allocation Allocation data
   */
  private updateItemVisualState(index: number, allocation: AllocationItem): void {
    const element = this.items.get(index);
    if (!element) return;

    // Remove existing state classes
    element.classList.remove('allocation-normal', 'allocation-warning', 'allocation-error');

    // Add appropriate state class
    if (allocation.percentage > allocation.maxAllowed) {
      element.classList.add('allocation-warning');
    } else if (allocation.value > 0) {
      element.classList.add('allocation-normal');
    }

    // Add error state if over-allocated globally
    const state = this.patrimonySync.getState();
    if (state.isOverAllocated) {
      element.classList.add('allocation-error');
    }
  }

  /**
   * Get total allocated value excluding a specific item
   * @param excludeIndex Index to exclude from calculation
   * @returns Total allocated value excluding the specified item
   */
  getTotalAllocatedExcept(excludeIndex: number): number {
    return this.patrimonySync
      .getAllAllocations()
      .filter((allocation) => allocation.index !== excludeIndex)
      .reduce((total, allocation) => total + allocation.value, 0);
  }

  /**
   * Handle validation errors
   * @param index Allocation index
   * @param attemptedValue The value that caused the error
   * @param error The error that occurred
   */
  private handleValidationError(index: number, attemptedValue: number, error: Error): void {
    console.warn(`Validation error for allocation ${index}:`, error.message);

    // Revert to previous valid value
    const allocation = this.patrimonySync.getAllocation(index);
    if (allocation) {
      this.updateItemDisplay(index);
    }

    this.emit('validationFailed', {
      index,
      attemptedValue,
      error: error.message,
      currentValue: allocation?.value || 0,
    });
  }

  /**
   * Setup event listeners for PatrimonySync events
   */
  private setupEventListeners(): void {
    this.patrimonySync.on('mainValueChanged', () => {
      this.updateAllItemDisplays();
    });

    this.patrimonySync.on('allocationChanged', (event: AllocationChangeEvent) => {
      this.updateItemDisplay(event.index);
    });

    this.patrimonySync.on('statusChanged', () => {
      this.updateAllItemVisualStates();
    });

    this.patrimonySync.on('overflowDetected', (data: any) => {
      if (this.validationConfig.proportionalAdjustment) {
        // Only apply adjustment if it's a significant overflow to prevent infinite loops
        if (data.overflowAmount > 0.01) {
          this.applyProportionalAdjustment();
        }
      }
    });

    this.patrimonySync.on('proportionalAdjustmentApplied', () => {
      this.updateAllItemDisplays();
      this.emit('syncCompleted', {
        totalAllocated: this.patrimonySync.getTotalAllocated(),
        remaining: this.patrimonySync.getRemainingValue(),
      });
    });
  }

  /**
   * Update displays for all items
   */
  private updateAllItemDisplays(): void {
    this.items.forEach((element, index) => {
      this.updateItemDisplay(index);
    });
  }

  /**
   * Update visual states for all items
   */
  private updateAllItemVisualStates(): void {
    this.items.forEach((element, index) => {
      const allocation = this.patrimonySync.getAllocation(index);
      if (allocation) {
        this.updateItemVisualState(index, allocation);
      }
    });
  }

  /**
   * Parse input value from string to number
   * @param value String value from input
   * @returns Parsed number value
   */
  private parseInputValue(value: string): number {
    // Remove currency symbols and spaces, handle Brazilian number format
    let cleanValue = value.replace(/[R$\s]/g, '');

    // Handle Brazilian decimal format (25.000,50 -> 25000.50)
    if (cleanValue.includes('.') && cleanValue.includes(',')) {
      // Both dots and commas present - dots are thousands separators, comma is decimal
      cleanValue = cleanValue.replace(/\./g, '').replace(',', '.');
    } else if (cleanValue.includes(',')) {
      // Only comma present - could be decimal separator
      const parts = cleanValue.split(',');
      if (parts.length === 2 && parts[1] && parts[1].length <= 2) {
        // Likely decimal separator
        cleanValue = cleanValue.replace(',', '.');
      } else {
        // Likely thousands separator
        cleanValue = cleanValue.replace(/,/g, '');
      }
    }

    const parsed = parseFloat(cleanValue);
    return isNaN(parsed) ? 0 : parsed;
  }

  /**
   * Format currency value
   * @param value Value to format
   * @returns Formatted currency string
   */
  private formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  }

  /**
   * Get all managed allocation items
   * @returns Array of allocation items
   */
  getAllocations(): AllocationItem[] {
    return this.patrimonySync.getAllAllocations();
  }

  /**
   * Get a specific allocation by index
   * @param index Allocation index
   * @returns Allocation item or undefined
   */
  getAllocation(index: number): AllocationItem | undefined {
    return this.patrimonySync.getAllocation(index);
  }

  /**
   * Get the DOM element for an allocation item
   * @param index Allocation index
   * @returns HTML element or undefined
   */
  getItemElement(index: number): HTMLElement | undefined {
    return this.items.get(index);
  }

  /**
   * Get the input element for an allocation item
   * @param index Allocation index
   * @returns Input element or undefined
   */
  getItemInput(index: number): HTMLInputElement | undefined {
    return this.inputs.get(index);
  }

  /**
   * Check if the manager is initialized
   * @returns True if initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Update validation configuration
   * @param config New validation configuration
   */
  updateValidationConfig(config: Partial<AllocationValidationConfig>): void {
    this.validationConfig = { ...this.validationConfig, ...config };
    this.patrimonySync.updateValidationConfig(config);
  }

  /**
   * Reset all allocations
   */
  resetAllocations(): void {
    this.patrimonySync.clearAllocations();
    this.updateAllItemDisplays();

    this.emit('allocationsReset', {
      itemCount: this.items.size,
    });
  }

  /**
   * Destroy the allocation manager and clean up resources
   */
  destroy(): void {
    // Remove event listeners from PatrimonySync
    this.patrimonySync.off('mainValueChanged', this.updateAllItemDisplays.bind(this));
    this.patrimonySync.off('statusChanged', this.updateAllItemVisualStates.bind(this));

    // Note: DOM event listeners are automatically cleaned up when elements are removed
    // or can be cleaned up by cloning and replacing elements if needed

    // Clear references
    this.items.clear();
    this.inputs.clear();
    this.sliders.clear();

    this.isInitialized = false;

    this.emit('destroyed');
    this.removeAllListeners();
  }
}
