import { ANIMATION_VARIANTS, MotionController } from './MotionController';
import type { AnimationVariant, VisibilityOptions } from './types';

/**
 * Section visibility controller using native Framer Motion useInView
 * Much simpler than Intersection Observer implementation
 */
export class SectionVisibilityController {
  private motionController: MotionController;

  constructor() {
    this.motionController = MotionController.getInstance();
  }

  /**
   * Get useInView options for sections
   */
  getInViewOptions(): VisibilityOptions {
    return {
      threshold: 0.1,
      triggerOnce: true,
      margin: '-50px',
    };
  }

  /**
   * Get section fade in variants
   */
  getSectionFadeInVariants(): AnimationVariant {
    return ANIMATION_VARIANTS.fadeIn;
  }

  /**
   * Get section slide up variants
   */
  getSectionSlideUpVariants(): AnimationVariant {
    return ANIMATION_VARIANTS.slideUp;
  }

  /**
   * Get stagger container variants
   */
  getStaggerContainerVariants(): AnimationVariant {
    return ANIMATION_VARIANTS.staggerContainer;
  }

  /**
   * Get stagger item variants
   */
  getStaggerItemVariants(): AnimationVariant {
    return ANIMATION_VARIANTS.staggerItem;
  }

  /**
   * Get float component variants
   */
  getFloatComponentVariants(): AnimationVariant {
    return ANIMATION_VARIANTS.floatComponent;
  }

  /**
   * Apply visibility animations to existing elements
   */
  refreshVisibility(): void {
    // Animate in sections
    const animateSections = document.querySelectorAll('[data-animate-in], .animate-on-scroll');
    animateSections.forEach((section) => {
      section.setAttribute('data-animation-variant', 'fadeIn');
    });

    // Float components
    const floatComponents = document.querySelectorAll('.float-component, [data-float-component]');
    floatComponents.forEach((component) => {
      component.setAttribute('data-animation-variant', 'floatComponent');
    });

    // Stagger containers
    const staggerContainers = document.querySelectorAll('[data-stagger-container]');
    staggerContainers.forEach((container) => {
      container.setAttribute('data-animation-variant', 'staggerContainer');

      // Mark children as stagger items
      const children = container.querySelectorAll('[data-stagger-item]');
      children.forEach((child) => {
        child.setAttribute('data-animation-variant', 'staggerItem');
      });
    });
  }

  /**
   * Get all available visibility variants
   */
  getAllVisibilityVariants(): Record<string, AnimationVariant> {
    return {
      fadeIn: this.getSectionFadeInVariants(),
      slideUp: this.getSectionSlideUpVariants(),
      staggerContainer: this.getStaggerContainerVariants(),
      staggerItem: this.getStaggerItemVariants(),
      floatComponent: this.getFloatComponentVariants(),
    };
  }

  /**
   * Cleanup method - nothing to destroy with useInView
   */
  destroy(): void {
    // No cleanup needed for useInView
  }
}
