import { CurrencyController } from './CurrencyController';

interface ButtonPair {
  increment?: HTMLButtonElement;
  decrement?: HTMLButtonElement;
  input: HTMLInputElement;
}

/**
 * CurrencyButtons - APENAS MODULARIZA o código original do Webflow
 * Código extraído diretamente de: <div class="codigo-controle-secao-currency w-embed w-script">
 * NÃO adiciona funcionalidade nova, apenas organiza o código existente em TypeScript
 */
export class CurrencyButtons {
  private controller: CurrencyController;
  private buttonPairs: Map<HTMLInputElement, ButtonPair> = new Map();
  private initialized = false;

  constructor(controller: CurrencyController) {
    this.controller = controller;
  }

  /**
   * Executa o código ORIGINAL do Webflow para botões de currency
   * Este é o código EXATO que estava funcionando no HTML
   */
  setupButtons(input?: HTMLInputElement, container?: HTMLElement): void {
    if (this.initialized) return;
    this.initialized = true;

    // CÓDIGO ORIGINAL DO WEBFLOW - EXTRAÍDO DA TAG <script>
    // Setup decrease buttons
    document.querySelectorAll('[currency-control="decrease"]').forEach(btn => {
      btn.addEventListener('click', function() {
        const targetInput = (this as HTMLElement).closest('.currency_buttons-wrapper')?.previousElementSibling?.querySelector('input') as HTMLInputElement;
        if (targetInput) {
          decreaseValue(targetInput);
        }
      });
    });

    // Setup increase buttons  
    document.querySelectorAll('[currency-control="increase"]').forEach(btn => {
      btn.addEventListener('click', function() {
        const targetInput = (this as HTMLElement).closest('.currency_buttons-wrapper')?.previousElementSibling?.querySelector('input') as HTMLInputElement;
        if (targetInput) {
          increaseValue(targetInput);
        }
      });
    });

    // FUNÇÕES ORIGINAIS DO WEBFLOW (código exato)
    function decreaseValue(input: HTMLInputElement) {
      const currentValue = getCurrencyValue(input);
      const newValue = Math.max(0, currentValue - 1000);
      updateInputValue(input, newValue);
    }

    function increaseValue(input: HTMLInputElement) {
      const currentValue = getCurrencyValue(input);
      const newValue = currentValue + 1000;
      updateInputValue(input, newValue);
    }

    function getCurrencyValue(input: HTMLInputElement) {
      const cleanValue = input.value.replace(/[^\d,]/g, '').replace(',', '.');
      return parseFloat(cleanValue) || 0;
    }

    function updateInputValue(input: HTMLInputElement, value: number) {
      const formatted = new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
      input.value = formatted;
      
      // Dispara evento original do Webflow
      input.dispatchEvent(new CustomEvent('currencyChange', {
        detail: {
          value: value,
          currencyValue: (window as any).currency ? (window as any).currency(value) : value,
          formatted: new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
          }).format(value)
        }
      }));
    }
  }

  /**
   * Remove button functionality
   */
  removeButtons(input: HTMLInputElement): void {
    this.buttonPairs.delete(input);
  }

  /**
   * Get button pair for an input
   */
  getButtonPair(input: HTMLInputElement): ButtonPair | undefined {
    return this.buttonPairs.get(input);
  }
}
