import { <PERSON><PERSON>rencyController } from './CurrencyController';
import type { CurrencyChangeEvent } from './types';

/**
 * CurrencyButtons manages increment/decrement buttons for currency inputs
 * Provides visual feedback and enhanced button interactions
 */
export class CurrencyButtons {
  private controller: CurrencyController;
  private buttonPairs: Map<HTMLInputElement, ButtonPair> = new Map();

  constructor(controller: CurrencyController) {
    this.controller = controller;
  }

  /**
   * Setup currency control buttons for an input
   * @param input HTML input element
   * @param container Optional container element to search for buttons
   */
  setupButtons(input: HTMLInputElement, container?: HTMLElement): void {
    const searchContainer = container || input.parentElement;
    if (!searchContainer) {
      console.warn('CurrencyButtons: No container found for button search');
      return;
    }

    // Find increment and decrement buttons
    const incrementBtn = searchContainer.querySelector(
      '[data-currency-increment]'
    ) as HTMLButtonElement;
    const decrementBtn = searchContainer.querySelector(
      '[data-currency-decrement]'
    ) as HTMLButtonElement;

    if (!incrementBtn && !decrementBtn) {
      return; // No buttons found, skip setup
    }

    const buttonPair: ButtonPair = {
      increment: incrementBtn,
      decrement: decrementBtn,
      input: input,
    };

    this.buttonPairs.set(input, buttonPair);

    // Setup button event handlers
    if (incrementBtn) {
      this.setupButtonEvents(incrementBtn, 'increment', input);
    }

    if (decrementBtn) {
      this.setupButtonEvents(decrementBtn, 'decrement', input);
    }

    // Setup visual feedback
    this.setupVisualFeedback(buttonPair);
  }

  /**
   * Setup event handlers for a button
   * @param button Button element
   * @param action Action type ('increment' or 'decrement')
   * @param input Associated input element
   */
  private setupButtonEvents(
    button: HTMLButtonElement,
    action: 'increment' | 'decrement',
    input: HTMLInputElement
  ): void {
    // Prevent form submission
    button.type = 'button';

    // Click handler
    button.addEventListener('click', (event) => {
      event.preventDefault();
      this.handleButtonClick(action, input, button);
    });

    // Mouse down/up for press effect
    button.addEventListener('mousedown', () => {
      this.addPressEffect(button);
    });

    button.addEventListener('mouseup', () => {
      this.removePressEffect(button);
    });

    button.addEventListener('mouseleave', () => {
      this.removePressEffect(button);
    });

    // Touch events for mobile
    button.addEventListener('touchstart', (event) => {
      event.preventDefault();
      this.addPressEffect(button);
    });

    button.addEventListener('touchend', (event) => {
      event.preventDefault();
      this.removePressEffect(button);
      this.handleButtonClick(action, input, button);
    });

    // Keyboard support
    button.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        this.addPressEffect(button);
        this.handleButtonClick(action, input, button);
      }
    });

    button.addEventListener('keyup', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        this.removePressEffect(button);
      }
    });
  }

  /**
   * Handle button click action
   * @param action Action type
   * @param input Input element
   * @param button Button element
   */
  private handleButtonClick(
    action: 'increment' | 'decrement',
    input: HTMLInputElement,
    button: HTMLButtonElement
  ): void {
    const currentValue = this.controller.getValue(input);
    const increment = this.getSmartIncrement(currentValue);

    let newValue: number;
    if (action === 'increment') {
      newValue = currentValue + increment;
    } else {
      newValue = currentValue - increment;
    }

    // Update the input value
    this.controller.setValue(input, newValue);

    // Add visual feedback
    this.addClickFeedback(button);

    // Focus the input after button click
    input.focus();
  }

  /**
   * Get smart increment value based on current value
   * @param value Current value
   * @returns Increment amount
   */
  private getSmartIncrement(value: number): number {
    const absValue = Math.abs(value);

    if (absValue < 10) return 1;
    if (absValue < 100) return 10;
    if (absValue < 1000) return 50;
    if (absValue < 10000) return 100;
    if (absValue < 100000) return 500;
    return 1000;
  }

  /**
   * Setup visual feedback for button interactions
   * @param buttonPair Button pair configuration
   */
  private setupVisualFeedback(buttonPair: ButtonPair): void {
    const { increment, decrement, input } = buttonPair;

    // Add CSS classes for styling
    if (increment) {
      increment.classList.add('currency-btn', 'currency-btn--increment');
      increment.setAttribute('aria-label', 'Increase value');
    }

    if (decrement) {
      decrement.classList.add('currency-btn', 'currency-btn--decrement');
      decrement.setAttribute('aria-label', 'Decrease value');
    }

    // Update button states based on input value
    this.updateButtonStates(buttonPair);

    // Listen for input changes to update button states
    this.controller.on('currencyChange', (event: CurrencyChangeEvent) => {
      if (event.input === input) {
        this.updateButtonStates(buttonPair);
      }
    });
  }

  /**
   * Update button states based on current value and constraints
   * @param buttonPair Button pair configuration
   */
  private updateButtonStates(buttonPair: ButtonPair): void {
    const { increment, decrement, input } = buttonPair;
    const currentValue = this.controller.getValue(input);

    // Get input configuration to check constraints
    const inputs = this.controller.getInitializedInputs();
    const inputIndex = inputs.indexOf(input);

    // For now, we'll assume no strict constraints unless specified
    // In a real implementation, you'd get these from the controller
    const minValue = 0; // Could be retrieved from controller config
    const maxValue = Number.MAX_SAFE_INTEGER; // Could be retrieved from controller config

    // Update increment button state
    if (increment) {
      const wouldExceedMax = currentValue >= maxValue;
      increment.disabled = wouldExceedMax;
      increment.classList.toggle('currency-btn--disabled', wouldExceedMax);
    }

    // Update decrement button state
    if (decrement) {
      const wouldExceedMin = currentValue <= minValue;
      decrement.disabled = wouldExceedMin;
      decrement.classList.toggle('currency-btn--disabled', wouldExceedMin);
    }
  }

  /**
   * Add press effect to button
   * @param button Button element
   */
  private addPressEffect(button: HTMLButtonElement): void {
    button.classList.add('currency-btn--pressed');
  }

  /**
   * Remove press effect from button
   * @param button Button element
   */
  private removePressEffect(button: HTMLButtonElement): void {
    button.classList.remove('currency-btn--pressed');
  }

  /**
   * Add click feedback animation
   * @param button Button element
   */
  private addClickFeedback(button: HTMLButtonElement): void {
    button.classList.add('currency-btn--clicked');

    // Remove the class after animation completes
    setTimeout(() => {
      button.classList.remove('currency-btn--clicked');
    }, 200);
  }

  /**
   * Create ripple effect on button click
   * @param button Button element
   * @param event Mouse or touch event
   */
  private createRippleEffect(button: HTMLButtonElement, event: MouseEvent | TouchEvent): void {
    const rect = button.getBoundingClientRect();
    const ripple = document.createElement('span');

    // Calculate ripple position
    let x: number, y: number;
    if (event instanceof MouseEvent) {
      x = event.clientX - rect.left;
      y = event.clientY - rect.top;
    } else {
      const touch = event.touches[0] || event.changedTouches[0];
      if (touch) {
        x = touch.clientX - rect.left;
        y = touch.clientY - rect.top;
      } else {
        x = rect.width / 2;
        y = rect.height / 2;
      }
    }

    // Style the ripple
    ripple.className = 'currency-btn__ripple';
    ripple.style.left = `${x}px`;
    ripple.style.top = `${y}px`;

    // Add ripple to button
    button.appendChild(ripple);

    // Remove ripple after animation
    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple);
      }
    }, 600);
  }

  /**
   * Remove button functionality for an input
   * @param input Input element
   */
  removeButtons(input: HTMLInputElement): void {
    const buttonPair = this.buttonPairs.get(input);
    if (buttonPair) {
      // Remove event listeners would require storing references
      // For now, we'll just remove from our tracking
      this.buttonPairs.delete(input);

      // Remove CSS classes
      if (buttonPair.increment) {
        buttonPair.increment.classList.remove('currency-btn', 'currency-btn--increment');
      }

      if (buttonPair.decrement) {
        buttonPair.decrement.classList.remove('currency-btn', 'currency-btn--decrement');
      }
    }
  }

  /**
   * Get button pair for an input
   * @param input Input element
   * @returns Button pair or undefined
   */
  getButtonPair(input: HTMLInputElement): ButtonPair | undefined {
    return this.buttonPairs.get(input);
  }

  /**
   * Get all managed button pairs
   * @returns Array of button pairs
   */
  getAllButtonPairs(): ButtonPair[] {
    return Array.from(this.buttonPairs.values());
  }
}

/**
 * Interface for button pair configuration
 */
interface ButtonPair {
  increment: HTMLButtonElement | null;
  decrement: HTMLButtonElement | null;
  input: HTMLInputElement;
}
