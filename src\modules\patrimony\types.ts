// Patrimony module types

export interface CurrencyValue {
  value: number;
  formatted: string;
  currency: string;
}

export interface AllocationItem {
  index: number;
  category: string;
  subcategory: string;
  value: number;
  percentage: number;
  maxAllowed: number;
}

export interface PatrimonyState {
  mainValue: number;
  totalAllocated: number;
  remaining: number;
  isFullyAllocated: boolean;
  isOverAllocated: boolean;
  percentageAllocated: number;
}

export interface AllocationChangeEvent {
  index: number;
  value: number;
  percentage: number;
  formatted: string;
  remaining: number;
}

export interface PatrimonyStatusEvent {
  mainValue: number;
  totalAllocated: number;
  remaining: number;
  isFullyAllocated: boolean;
  isOverAllocated: boolean;
  percentageAllocated: number;
}

export interface CacheData {
  mainValue: number;
  allocations: Map<number, AllocationItem>;
  timestamp: number;
  version: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  totalPercentage: number;
  overflowAmount: number;
}

export interface AllocationValidationConfig {
  maxPercentage: number;
  allowOverflow: boolean;
  proportionalAdjustment: boolean;
}
