import type { DOMUtilsConfig } from './types';

/**
 * Utility class for common DOM operations
 * Provides helper methods for element selection, manipulation, and event handling
 */
export class DOMUtils {
  private static config: DOMUtilsConfig = {
    debounceDelay: 300,
    throttleDelay: 100,
  };

  /**
   * Configure DOMUtils behavior
   * @param config - Configuration options
   */
  static configure(config: Partial<DOMUtilsConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Safe querySelector that throws descriptive errors
   * @param selector - CSS selector
   * @param context - Context element (defaults to document)
   * @returns Found element
   * @throws Error if element not found
   */
  static querySelector<T extends HTMLElement = HTMLElement>(
    selector: string,
    context: Document | HTMLElement = document
  ): T {
    const element = context.querySelector<T>(selector);
    if (!element) {
      throw new Error(`Element not found: ${selector}`);
    }
    return element;
  }

  /**
   * Safe querySelectorAll
   * @param selector - CSS selector
   * @param context - Context element (defaults to document)
   * @returns NodeList of found elements
   */
  static querySelectorAll<T extends HTMLElement = HTMLElement>(
    selector: string,
    context: Document | HTMLElement = document
  ): NodeListOf<T> {
    return context.querySelectorAll<T>(selector);
  }

  /**
   * Safe querySelector that returns null instead of throwing
   * @param selector - CSS selector
   * @param context - Context element (defaults to document)
   * @returns Found element or null
   */
  static querySelectorSafe<T extends HTMLElement = HTMLElement>(
    selector: string,
    context: Document | HTMLElement = document
  ): T | null {
    return context.querySelector<T>(selector);
  }

  /**
   * Add event listener with automatic cleanup tracking
   * @param element - Target element
   * @param event - Event type
   * @param handler - Event handler
   * @param options - Event listener options
   * @returns Cleanup function
   */
  static addEventListener<K extends keyof HTMLElementEventMap>(
    element: HTMLElement,
    event: K,
    handler: (event: HTMLElementEventMap[K]) => void,
    options?: AddEventListenerOptions
  ): () => void {
    element.addEventListener(event, handler, options);
    return () => element.removeEventListener(event, handler, options);
  }

  /**
   * Debounce function calls
   * @param func - Function to debounce
   * @param delay - Delay in milliseconds (defaults to config value)
   * @returns Debounced function
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number = this.config.debounceDelay
  ): (...args: Parameters<T>) => void {
    let timeoutId: ReturnType<typeof setTimeout>;

    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }

  /**
   * Throttle function calls
   * @param func - Function to throttle
   * @param delay - Delay in milliseconds (defaults to config value)
   * @returns Throttled function
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number = this.config.throttleDelay
  ): (...args: Parameters<T>) => void {
    let lastCall = 0;

    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  }

  /**
   * Wait for DOM to be ready
   * @returns Promise that resolves when DOM is ready
   */
  static ready(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => resolve());
      } else {
        resolve();
      }
    });
  }

  /**
   * Create element with attributes and content
   * @param tagName - HTML tag name
   * @param attributes - Element attributes
   * @param content - Text content or child elements
   * @returns Created element
   */
  static createElement<K extends keyof HTMLElementTagNameMap>(
    tagName: K,
    attributes: Record<string, string> = {},
    content?: string | HTMLElement[]
  ): HTMLElementTagNameMap[K] {
    const element = document.createElement(tagName);

    // Set attributes
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, value);
    });

    // Set content
    if (typeof content === 'string') {
      element.textContent = content;
    } else if (Array.isArray(content)) {
      content.forEach((child) => element.appendChild(child));
    }

    return element;
  }

  /**
   * Check if element is visible in viewport
   * @param element - Element to check
   * @param threshold - Visibility threshold (0-1)
   * @returns True if element is visible
   */
  static isInViewport(element: HTMLElement, threshold: number = 0): boolean {
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;

    const verticalVisible =
      rect.top <= windowHeight * (1 - threshold) && rect.bottom >= windowHeight * threshold;
    const horizontalVisible =
      rect.left <= windowWidth * (1 - threshold) && rect.right >= windowWidth * threshold;

    return verticalVisible && horizontalVisible;
  }

  /**
   * Animate element using CSS transitions
   * @param element - Element to animate
   * @param properties - CSS properties to animate
   * @param duration - Animation duration in milliseconds
   * @returns Promise that resolves when animation completes
   */
  static animate(
    element: HTMLElement,
    properties: Partial<CSSStyleDeclaration>,
    duration: number = 300
  ): Promise<void> {
    return new Promise((resolve) => {
      const originalTransition = element.style.transition;

      // Set transition
      element.style.transition = `all ${duration}ms ease`;

      // Apply properties
      Object.assign(element.style, properties);

      // Wait for animation to complete
      setTimeout(() => {
        element.style.transition = originalTransition;
        resolve();
      }, duration);
    });
  }

  /**
   * Get computed style value
   * @param element - Target element
   * @param property - CSS property name
   * @returns Computed style value
   */
  static getComputedStyle(element: HTMLElement, property: string): string {
    return window.getComputedStyle(element).getPropertyValue(property);
  }

  /**
   * Safely parse JSON from element dataset or attribute
   * @param element - Target element
   * @param key - Data attribute key
   * @param fallback - Fallback value if parsing fails
   * @returns Parsed value or fallback
   */
  static parseDataAttribute<T>(element: HTMLElement, key: string, fallback: T): T {
    try {
      const value = element.dataset[key];
      return value ? JSON.parse(value) : fallback;
    } catch {
      return fallback;
    }
  }
}
