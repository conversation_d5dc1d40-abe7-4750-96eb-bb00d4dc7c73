import { PatrimonySync } from '../../../src/modules/patrimony/PatrimonySync';
import { AllocationItem } from '../../../src/modules/patrimony/types';

describe('PatrimonySync', () => {
  let patrimonySync: PatrimonySync;

  beforeEach(() => {
    // Reset singleton instance for each test
    (PatrimonySync as any).instance = undefined;
    patrimonySync = PatrimonySync.getInstance();
    patrimonySync.reset();
  });

  afterEach(() => {
    patrimonySync.removeAllListeners();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = PatrimonySync.getInstance();
      const instance2 = PatrimonySync.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Main Value Management', () => {
    it('should set and get main value', () => {
      patrimonySync.setMainValue(100000);
      expect(patrimonySync.getMainValue()).toBe(100000);
    });

    it('should throw error for negative main value', () => {
      expect(() => patrimonySync.setMainValue(-1000)).toThrow('Main value cannot be negative');
    });

    it('should emit mainValueChanged event', () => {
      const mockCallback = jest.fn();
      patrimonySync.on('mainValueChanged', mockCallback);

      patrimonySync.setMainValue(50000);

      expect(mockCallback).toHaveBeenCalledWith({
        previousValue: 0,
        newValue: 50000,
        allocations: [],
      });
    });

    it('should recalculate allocation percentages when main value changes', () => {
      // Add allocation first
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 25000,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.setMainValue(100000);
      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 25000);

      expect(patrimonySync.getAllocation(1)?.percentage).toBe(25);

      // Change main value
      patrimonySync.setMainValue(50000);
      expect(patrimonySync.getAllocation(1)?.percentage).toBe(50);
    });
  });

  describe('Allocation Management', () => {
    beforeEach(() => {
      patrimonySync.setMainValue(100000);
    });

    it('should add allocation', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      expect(patrimonySync.getAllocation(1)).toEqual(allocation);
    });

    it('should throw error when adding duplicate allocation', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      expect(() => patrimonySync.addAllocation(allocation)).toThrow(
        'Allocation with index 1 already exists'
      );
    });

    it('should update allocation value and percentage', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 30000);

      const updated = patrimonySync.getAllocation(1);
      expect(updated?.value).toBe(30000);
      expect(updated?.percentage).toBe(30);
    });

    it('should emit allocationChanged event', () => {
      const mockCallback = jest.fn();
      patrimonySync.on('allocationChanged', mockCallback);

      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 25000);

      expect(mockCallback).toHaveBeenCalledWith({
        index: 1,
        value: 25000,
        percentage: 25,
        formatted: expect.stringContaining('25.000'),
        remaining: 75000,
      });
    });

    it('should throw error for negative allocation value', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      expect(() => patrimonySync.updateAllocation(1, -1000)).toThrow(
        'Allocation value cannot be negative'
      );
    });

    it('should remove allocation', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.removeAllocation(1);
      expect(patrimonySync.getAllocation(1)).toBeUndefined();
    });

    it('should get all allocations', () => {
      const allocation1: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      const allocation2: AllocationItem = {
        index: 2,
        category: 'Bonds',
        subcategory: 'Government',
        value: 0,
        percentage: 0,
        maxAllowed: 30,
      };

      patrimonySync.addAllocation(allocation1);
      patrimonySync.addAllocation(allocation2);

      const allAllocations = patrimonySync.getAllAllocations();
      expect(allAllocations).toHaveLength(2);
      expect(allAllocations.find((a) => a.index === 1)).toEqual(allocation1);
      expect(allAllocations.find((a) => a.index === 2)).toEqual(allocation2);
    });
  });

  describe('Calculations', () => {
    beforeEach(() => {
      patrimonySync.setMainValue(100000);
    });

    it('should calculate total allocated', () => {
      const allocation1: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      const allocation2: AllocationItem = {
        index: 2,
        category: 'Bonds',
        subcategory: 'Government',
        value: 0,
        percentage: 0,
        maxAllowed: 30,
      };

      patrimonySync.addAllocation(allocation1);
      patrimonySync.addAllocation(allocation2);
      patrimonySync.updateAllocation(1, 30000);
      patrimonySync.updateAllocation(2, 20000);

      expect(patrimonySync.getTotalAllocated()).toBe(50000);
    });

    it('should calculate remaining value', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 30000);

      expect(patrimonySync.getRemainingValue()).toBe(70000);
    });

    it('should return 0 for remaining value when over-allocated', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.updateValidationConfig({
        allowOverflow: true,
        proportionalAdjustment: false,
      });
      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 120000);

      expect(patrimonySync.getRemainingValue()).toBe(0);
    });
  });

  describe('State Management', () => {
    beforeEach(() => {
      patrimonySync.setMainValue(100000);
    });

    it('should return correct state', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 50000);

      const state = patrimonySync.getState();
      expect(state).toEqual({
        mainValue: 100000,
        totalAllocated: 50000,
        remaining: 50000,
        isFullyAllocated: false,
        isOverAllocated: false,
        percentageAllocated: 50,
      });
    });

    it('should detect fully allocated state', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 100,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 100000);

      const state = patrimonySync.getState();
      expect(state.isFullyAllocated).toBe(true);
      expect(state.remaining).toBe(0);
    });

    it('should detect over-allocated state', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 150,
      };

      patrimonySync.updateValidationConfig({
        allowOverflow: true,
        proportionalAdjustment: false,
      });
      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 120000);

      const state = patrimonySync.getState();
      expect(state.isOverAllocated).toBe(true);
      expect(state.percentageAllocated).toBe(120);
    });

    it('should emit statusChanged event', () => {
      const mockCallback = jest.fn();
      patrimonySync.on('statusChanged', mockCallback);

      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);

      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          mainValue: 100000,
          totalAllocated: 0,
          remaining: 100000,
          isFullyAllocated: false,
          isOverAllocated: false,
          percentageAllocated: 0,
        })
      );
    });
  });

  describe('Validation', () => {
    beforeEach(() => {
      patrimonySync.setMainValue(100000);
    });

    it('should validate allocations successfully', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 30000);

      const validation = patrimonySync.validateAllocations();
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validation.totalPercentage).toBe(30);
      expect(validation.overflowAmount).toBe(0);
    });

    it('should detect overflow validation error', () => {
      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 150,
      };

      patrimonySync.updateValidationConfig({
        allowOverflow: true,
        proportionalAdjustment: false,
      });
      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 120000);

      const validation = patrimonySync.validateAllocations();
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(
        'Total allocation (120.00%) exceeds maximum allowed (100%)'
      );
      expect(validation.overflowAmount).toBe(20000);
    });

    it('should emit overflowDetected event', () => {
      const mockCallback = jest.fn();
      patrimonySync.on('overflowDetected', mockCallback);

      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 150,
      };

      patrimonySync.updateValidationConfig({
        allowOverflow: true,
        proportionalAdjustment: false,
      });
      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 120000);

      expect(mockCallback).toHaveBeenCalledWith({
        overflowAmount: 20000,
        totalPercentage: 120,
        allocations: expect.any(Array),
      });
    });
  });

  describe('Proportional Adjustment', () => {
    beforeEach(() => {
      patrimonySync.setMainValue(100000);
      patrimonySync.updateValidationConfig({
        allowOverflow: true,
        proportionalAdjustment: true,
      });
    });

    it('should apply proportional adjustment for overflow', () => {
      const allocation1: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 100,
      };

      const allocation2: AllocationItem = {
        index: 2,
        category: 'Bonds',
        subcategory: 'Government',
        value: 0,
        percentage: 0,
        maxAllowed: 100,
      };

      patrimonySync.addAllocation(allocation1);
      patrimonySync.addAllocation(allocation2);
      patrimonySync.updateAllocation(1, 80000);
      patrimonySync.updateAllocation(2, 40000); // Total: 120000 (20% overflow)

      // After proportional adjustment, values should be reduced by factor of 100000/120000 = 0.8333
      const adjusted1 = patrimonySync.getAllocation(1);
      const adjusted2 = patrimonySync.getAllocation(2);

      expect(adjusted1?.value).toBeCloseTo(66666.67, 0);
      expect(adjusted2?.value).toBeCloseTo(33333.33, 0);
      expect(patrimonySync.getTotalAllocated()).toBeCloseTo(100000, 0);
    });

    it('should emit proportionalAdjustmentApplied event', () => {
      const mockCallback = jest.fn();
      patrimonySync.on('proportionalAdjustmentApplied', mockCallback);

      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 150,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.updateAllocation(1, 120000);

      expect(mockCallback).toHaveBeenCalledWith({
        adjustmentFactor: expect.closeTo(0.8333, 3),
        allocations: expect.any(Array),
      });
    });
  });

  describe('Utility Methods', () => {
    it('should clear all allocations', () => {
      patrimonySync.setMainValue(100000);

      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.clearAllocations();

      expect(patrimonySync.getAllAllocations()).toHaveLength(0);
    });

    it('should reset to initial state', () => {
      patrimonySync.setMainValue(100000);

      const allocation: AllocationItem = {
        index: 1,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 0,
        percentage: 0,
        maxAllowed: 50,
      };

      patrimonySync.addAllocation(allocation);
      patrimonySync.reset();

      expect(patrimonySync.getMainValue()).toBe(0);
      expect(patrimonySync.getAllAllocations()).toHaveLength(0);
    });

    it('should update validation configuration', () => {
      patrimonySync.updateValidationConfig({
        maxPercentage: 120,
        allowOverflow: true,
      });

      const config = patrimonySync.getValidationConfig();
      expect(config.maxPercentage).toBe(120);
      expect(config.allowOverflow).toBe(true);
      expect(config.proportionalAdjustment).toBe(true); // Should keep existing value
    });
  });
});
