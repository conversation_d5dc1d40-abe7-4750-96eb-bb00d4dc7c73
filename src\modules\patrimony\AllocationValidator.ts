import { EventEmitter } from '../core/EventEmitter';
import {
  AllocationItem,
  ValidationResult,
  AllocationValidationConfig,
  PatrimonyState,
} from './types';

/**
 * AllocationValidator - Comprehensive validation system for allocation limits and constraints
 *
 * This class provides detailed validation logic for allocation items, overflow detection,
 * proportional reduction calculations, and validation feedback generation.
 *
 * Events emitted:
 * - 'validationCompleted': When validation is completed
 * - 'overflowDetected': When allocation overflow is detected
 * - 'warningGenerated': When validation warnings are generated
 * - 'proportionalReductionCalculated': When proportional reduction is calculated
 */
export class AllocationValidator extends EventEmitter {
  private config: AllocationValidationConfig;

  constructor(config?: Partial<AllocationValidationConfig>) {
    super();
    this.config = {
      maxPercentage: 100,
      allowOverflow: false,
      proportionalAdjustment: true,
      ...config,
    };
  }

  /**
   * Validate a collection of allocation items
   * @param allocations Array of allocation items
   * @param mainValue Main patrimony value
   * @returns Comprehensive validation result
   */
  validateAllocations(allocations: AllocationItem[], mainValue: number): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Calculate totals
    const totalValue = allocations.reduce((sum, allocation) => sum + allocation.value, 0);
    const totalPercentage = mainValue > 0 ? (totalValue / mainValue) * 100 : 0;
    const overflowAmount = Math.max(0, totalValue - mainValue);

    // Validate main value
    if (mainValue < 0) {
      errors.push('Main patrimony value cannot be negative');
    }

    if (mainValue === 0 && allocations.some((a) => a.value > 0)) {
      errors.push('Cannot allocate values when main patrimony value is zero');
    }

    // Validate individual allocations
    allocations.forEach((allocation, index) => {
      const itemErrors = this.validateSingleAllocation(allocation, mainValue);
      errors.push(...itemErrors.errors);
      warnings.push(...itemErrors.warnings);
    });

    // Validate total allocation
    if (totalPercentage > this.config.maxPercentage) {
      const message = `Total allocation (${totalPercentage.toFixed(2)}%) exceeds maximum allowed (${this.config.maxPercentage}%)`;

      if (this.config.allowOverflow) {
        warnings.push(message);
      } else {
        errors.push(message);
      }
    }

    // Check for duplicate categories
    const duplicateErrors = this.validateDuplicateCategories(allocations);
    errors.push(...duplicateErrors);

    // Validate allocation distribution
    const distributionWarnings = this.validateAllocationDistribution(allocations, totalPercentage);
    warnings.push(...distributionWarnings);

    const result: ValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings,
      totalPercentage,
      overflowAmount,
    };

    // Emit events
    this.emit('validationCompleted', {
      result,
      allocationCount: allocations.length,
      mainValue,
    });

    if (overflowAmount > 0) {
      this.emit('overflowDetected', {
        overflowAmount,
        totalPercentage,
        allocations: allocations.map((a) => ({ ...a })),
      });
    }

    if (warnings.length > 0) {
      this.emit('warningGenerated', {
        warnings,
        allocations: allocations.map((a) => ({ ...a })),
      });
    }

    return result;
  }

  /**
   * Validate a single allocation item
   * @param allocation Allocation item to validate
   * @param mainValue Main patrimony value
   * @returns Validation result for single item
   */
  validateSingleAllocation(allocation: AllocationItem, mainValue: number): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate basic properties
    if (allocation.value < 0) {
      errors.push(`Allocation ${allocation.index}: Value cannot be negative`);
    }

    if (allocation.percentage < 0) {
      errors.push(`Allocation ${allocation.index}: Percentage cannot be negative`);
    }

    if (allocation.maxAllowed < 0) {
      errors.push(`Allocation ${allocation.index}: Maximum allowed percentage cannot be negative`);
    }

    // Validate category and subcategory
    if (!allocation.category || allocation.category.trim() === '') {
      errors.push(`Allocation ${allocation.index}: Category is required`);
    }

    if (!allocation.subcategory || allocation.subcategory.trim() === '') {
      warnings.push(`Allocation ${allocation.index}: Subcategory is recommended`);
    }

    // Validate percentage consistency
    if (mainValue > 0) {
      const calculatedPercentage = (allocation.value / mainValue) * 100;
      const percentageDiff = Math.abs(calculatedPercentage - allocation.percentage);

      if (percentageDiff > 0.01) {
        // Allow small floating point differences
        warnings.push(
          `Allocation ${allocation.index}: Percentage (${allocation.percentage.toFixed(2)}%) doesn't match calculated value (${calculatedPercentage.toFixed(2)}%)`
        );
      }
    }

    // Validate against maximum allowed
    if (allocation.percentage > allocation.maxAllowed) {
      warnings.push(
        `Allocation ${allocation.index}: Percentage (${allocation.percentage.toFixed(2)}%) exceeds recommended maximum (${allocation.maxAllowed}%)`
      );
    }

    // Validate reasonable limits
    if (allocation.percentage > 80) {
      warnings.push(
        `Allocation ${allocation.index}: Very high allocation percentage (${allocation.percentage.toFixed(2)}%) may indicate poor diversification`
      );
    }

    if (allocation.value > 0 && allocation.percentage < 1) {
      warnings.push(
        `Allocation ${allocation.index}: Very small allocation percentage (${allocation.percentage.toFixed(2)}%) may not be significant`
      );
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      totalPercentage: allocation.percentage,
      overflowAmount: Math.max(0, allocation.value - (mainValue * allocation.maxAllowed) / 100),
    };
  }

  /**
   * Calculate proportional reduction for over-allocated items
   * @param allocations Array of allocation items
   * @param mainValue Main patrimony value
   * @returns Array of adjusted allocation items
   */
  calculateProportionalReduction(
    allocations: AllocationItem[],
    mainValue: number
  ): AllocationItem[] {
    const totalValue = allocations.reduce((sum, allocation) => sum + allocation.value, 0);

    if (totalValue <= mainValue) {
      return allocations; // No reduction needed
    }

    const reductionFactor = mainValue / totalValue;
    const adjustedAllocations: AllocationItem[] = [];

    allocations.forEach((allocation) => {
      const adjustedValue = allocation.value * reductionFactor;
      const adjustedPercentage = mainValue > 0 ? (adjustedValue / mainValue) * 100 : 0;

      adjustedAllocations.push({
        ...allocation,
        value: adjustedValue,
        percentage: adjustedPercentage,
      });
    });

    this.emit('proportionalReductionCalculated', {
      originalTotal: totalValue,
      adjustedTotal: mainValue,
      reductionFactor,
      adjustedAllocations: adjustedAllocations.map((a) => ({ ...a })),
    });

    return adjustedAllocations;
  }

  /**
   * Generate validation feedback messages for UI display
   * @param validationResult Validation result
   * @returns Formatted feedback messages
   */
  generateFeedbackMessages(validationResult: ValidationResult): {
    errorMessages: string[];
    warningMessages: string[];
    successMessage: string | null;
    suggestions: string[];
  } {
    const errorMessages = validationResult.errors.map((error) => `❌ ${error}`);
    const warningMessages = validationResult.warnings.map((warning) => `⚠️ ${warning}`);
    const suggestions: string[] = [];

    let successMessage: string | null = null;

    if (validationResult.isValid) {
      if (validationResult.warnings.length === 0) {
        successMessage = '✅ All allocations are valid and well-balanced';
      } else {
        successMessage = '✅ Allocations are valid but have some recommendations';
      }
    }

    // Generate suggestions based on validation results
    if (validationResult.overflowAmount > 0) {
      suggestions.push(
        `Consider reducing allocations by ${validationResult.overflowAmount.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })} to stay within limits`
      );
    }

    if (validationResult.totalPercentage < 80) {
      const remaining = 100 - validationResult.totalPercentage;
      suggestions.push(
        `You have ${remaining.toFixed(1)}% of your patrimony unallocated. Consider diversifying further.`
      );
    }

    if (validationResult.totalPercentage > 95 && validationResult.totalPercentage <= 100) {
      suggestions.push('Excellent allocation coverage! Your patrimony is well-distributed.');
    }

    return {
      errorMessages,
      warningMessages,
      successMessage,
      suggestions,
    };
  }

  /**
   * Validate for duplicate categories
   * @param allocations Array of allocation items
   * @returns Array of error messages
   */
  private validateDuplicateCategories(allocations: AllocationItem[]): string[] {
    const errors: string[] = [];
    const categoryMap = new Map<string, number[]>();

    allocations.forEach((allocation) => {
      const key = `${allocation.category}-${allocation.subcategory}`;
      if (!categoryMap.has(key)) {
        categoryMap.set(key, []);
      }
      categoryMap.get(key)!.push(allocation.index);
    });

    categoryMap.forEach((indices, categoryKey) => {
      if (indices.length > 1) {
        errors.push(
          `Duplicate category/subcategory "${categoryKey}" found in allocations: ${indices.join(', ')}`
        );
      }
    });

    return errors;
  }

  /**
   * Validate allocation distribution for diversification
   * @param allocations Array of allocation items
   * @param totalPercentage Total allocation percentage
   * @returns Array of warning messages
   */
  private validateAllocationDistribution(
    allocations: AllocationItem[],
    totalPercentage: number
  ): string[] {
    const warnings: string[] = [];

    // Check for over-concentration in single category
    const categoryTotals = new Map<string, number>();
    allocations.forEach((allocation) => {
      const current = categoryTotals.get(allocation.category) || 0;
      categoryTotals.set(allocation.category, current + allocation.percentage);
    });

    categoryTotals.forEach((percentage, category) => {
      if (percentage > 60) {
        warnings.push(
          `High concentration in "${category}" category (${percentage.toFixed(1)}%). Consider diversifying.`
        );
      }
    });

    // Check for too many small allocations
    const smallAllocations = allocations.filter((a) => a.percentage > 0 && a.percentage < 5);
    if (smallAllocations.length > 5) {
      warnings.push(
        `Many small allocations (${smallAllocations.length} items < 5%). Consider consolidating.`
      );
    }

    // Check for unbalanced distribution
    if (allocations.length > 1) {
      const avgPercentage = totalPercentage / allocations.filter((a) => a.value > 0).length;
      const highVariance = allocations.some(
        (a) => a.percentage > 0 && Math.abs(a.percentage - avgPercentage) > avgPercentage * 2
      );

      if (highVariance) {
        warnings.push(
          'Allocation distribution is highly unbalanced. Consider more even distribution.'
        );
      }
    }

    return warnings;
  }

  /**
   * Validate allocation against risk profile
   * @param allocations Array of allocation items
   * @param riskProfile Risk profile ('conservative' | 'moderate' | 'aggressive')
   * @returns Validation result with risk-specific recommendations
   */
  validateRiskProfile(
    allocations: AllocationItem[],
    riskProfile: 'conservative' | 'moderate' | 'aggressive'
  ): ValidationResult {
    const warnings: string[] = [];
    const errors: string[] = [];

    // Define risk profile limits
    const riskLimits = {
      conservative: { stocks: 40, bonds: 80, alternatives: 10 },
      moderate: { stocks: 70, bonds: 60, alternatives: 20 },
      aggressive: { stocks: 90, bonds: 40, alternatives: 30 },
    };

    const limits = riskLimits[riskProfile];

    // Categorize allocations by risk level
    const stocksPercentage = allocations
      .filter(
        (a) =>
          a.category.toLowerCase().includes('stock') || a.category.toLowerCase().includes('equity')
      )
      .reduce((sum, a) => sum + a.percentage, 0);

    const bondsPercentage = allocations
      .filter(
        (a) =>
          a.category.toLowerCase().includes('bond') || a.category.toLowerCase().includes('fixed')
      )
      .reduce((sum, a) => sum + a.percentage, 0);

    const alternativesPercentage = allocations
      .filter(
        (a) =>
          !a.category.toLowerCase().includes('stock') &&
          !a.category.toLowerCase().includes('equity') &&
          !a.category.toLowerCase().includes('bond') &&
          !a.category.toLowerCase().includes('fixed')
      )
      .reduce((sum, a) => sum + a.percentage, 0);

    // Validate against risk profile
    if (stocksPercentage > limits.stocks) {
      warnings.push(
        `Stock allocation (${stocksPercentage.toFixed(1)}%) exceeds ${riskProfile} profile recommendation (${limits.stocks}%)`
      );
    }

    if (bondsPercentage > limits.bonds) {
      warnings.push(
        `Bond allocation (${bondsPercentage.toFixed(1)}%) exceeds ${riskProfile} profile recommendation (${limits.bonds}%)`
      );
    }

    if (alternativesPercentage > limits.alternatives) {
      warnings.push(
        `Alternative investments (${alternativesPercentage.toFixed(1)}%) exceed ${riskProfile} profile recommendation (${limits.alternatives}%)`
      );
    }

    const totalPercentage = allocations.reduce((sum, a) => sum + a.percentage, 0);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      totalPercentage,
      overflowAmount: 0,
    };
  }

  /**
   * Update validation configuration
   * @param config New configuration options
   */
  updateConfig(config: Partial<AllocationValidationConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current validation configuration
   * @returns Current configuration
   */
  getConfig(): AllocationValidationConfig {
    return { ...this.config };
  }

  /**
   * Validate patrimony state consistency
   * @param state Patrimony state to validate
   * @returns Validation result
   */
  validatePatrimonyState(state: PatrimonyState): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate state consistency
    if (state.mainValue < 0) {
      errors.push('Main value cannot be negative');
    }

    if (state.totalAllocated < 0) {
      errors.push('Total allocated cannot be negative');
    }

    if (state.remaining < 0 && !state.isOverAllocated) {
      errors.push('Remaining value is negative but over-allocation flag is not set');
    }

    if (state.isOverAllocated && state.totalAllocated <= state.mainValue) {
      errors.push('Over-allocation flag is set but total is not greater than main value');
    }

    if (state.isFullyAllocated && state.remaining !== 0) {
      errors.push('Fully allocated flag is set but remaining value is not zero');
    }

    // Validate percentage calculation
    const calculatedPercentage =
      state.mainValue > 0 ? (state.totalAllocated / state.mainValue) * 100 : 0;
    if (Math.abs(calculatedPercentage - state.percentageAllocated) > 0.01) {
      warnings.push(
        `Percentage allocated (${state.percentageAllocated.toFixed(2)}%) doesn't match calculated value (${calculatedPercentage.toFixed(2)}%)`
      );
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      totalPercentage: state.percentageAllocated,
      overflowAmount: Math.max(0, state.totalAllocated - state.mainValue),
    };
  }
}
