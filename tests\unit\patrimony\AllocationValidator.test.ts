import { AllocationValidator } from '../../../src/modules/patrimony/AllocationValidator';
import { AllocationItem, PatrimonyState } from '../../../src/modules/patrimony/types';

describe('AllocationValidator', () => {
  let validator: AllocationValidator;
  let mockAllocations: AllocationItem[];

  beforeEach(() => {
    validator = new AllocationValidator();

    mockAllocations = [
      {
        index: 0,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 25000,
        percentage: 25,
        maxAllowed: 50,
      },
      {
        index: 1,
        category: 'Bonds',
        subcategory: 'Government',
        value: 35000,
        percentage: 35,
        maxAllowed: 40,
      },
    ];
  });

  afterEach(() => {
    validator.removeAllListeners();
  });

  describe('Basic Validation', () => {
    it('should validate allocations successfully', () => {
      const result = validator.validateAllocations(mockAllocations, 100000);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.totalPercentage).toBe(60);
      expect(result.overflowAmount).toBe(0);
    });

    it('should detect overflow', () => {
      const mockCallback = jest.fn();
      validator.on('overflowDetected', mockCallback);

      const result = validator.validateAllocations(mockAllocations, 50000); // Total value 60000 > 50000

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Total allocation (120.00%) exceeds maximum allowed (100%)');
      expect(result.overflowAmount).toBe(10000);
      expect(mockCallback).toHaveBeenCalled();
    });

    it('should allow overflow when configured', () => {
      validator.updateConfig({ allowOverflow: true });

      const result = validator.validateAllocations(mockAllocations, 50000);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain(
        'Total allocation (120.00%) exceeds maximum allowed (100%)'
      );
    });

    it('should emit validation completed event', () => {
      const mockCallback = jest.fn();
      validator.on('validationCompleted', mockCallback);

      validator.validateAllocations(mockAllocations, 100000);

      expect(mockCallback).toHaveBeenCalledWith({
        result: expect.any(Object),
        allocationCount: 2,
        mainValue: 100000,
      });
    });
  });

  describe('Single Allocation Validation', () => {
    it('should validate single allocation', () => {
      const result = validator.validateSingleAllocation(mockAllocations[0]!, 100000);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect negative values', () => {
      const allocation: AllocationItem = {
        index: 0,
        category: 'Stocks',
        subcategory: 'Tech',
        value: -1000,
        percentage: 25,
        maxAllowed: 50,
      };
      const result = validator.validateSingleAllocation(allocation, 100000);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Allocation 0: Value cannot be negative');
    });

    it('should detect missing category', () => {
      const allocation: AllocationItem = {
        index: 0,
        category: '',
        subcategory: 'Tech',
        value: 25000,
        percentage: 25,
        maxAllowed: 50,
      };
      const result = validator.validateSingleAllocation(allocation, 100000);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Allocation 0: Category is required');
    });

    it('should warn about missing subcategory', () => {
      const allocation: AllocationItem = {
        index: 0,
        category: 'Stocks',
        subcategory: '',
        value: 25000,
        percentage: 25,
        maxAllowed: 50,
      };
      const result = validator.validateSingleAllocation(allocation, 100000);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Allocation 0: Subcategory is recommended');
    });

    it('should warn about percentage exceeding max allowed', () => {
      const allocation: AllocationItem = {
        index: 0,
        category: 'Stocks',
        subcategory: 'Tech',
        value: 60000,
        percentage: 60,
        maxAllowed: 50,
      };
      const result = validator.validateSingleAllocation(allocation, 100000);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain(
        'Allocation 0: Percentage (60.00%) exceeds recommended maximum (50%)'
      );
    });
  });

  describe('Proportional Reduction', () => {
    it('should calculate proportional reduction', () => {
      const mockCallback = jest.fn();
      validator.on('proportionalReductionCalculated', mockCallback);

      const overAllocations: AllocationItem[] = [
        {
          index: 0,
          category: 'Stocks',
          subcategory: 'Tech',
          value: 80000,
          percentage: 80,
          maxAllowed: 100,
        },
        {
          index: 1,
          category: 'Bonds',
          subcategory: 'Gov',
          value: 40000,
          percentage: 40,
          maxAllowed: 100,
        },
      ];

      const adjusted = validator.calculateProportionalReduction(overAllocations, 100000);

      expect(adjusted[0]!.value).toBeCloseTo(66666.67, 0);
      expect(adjusted[1]!.value).toBeCloseTo(33333.33, 0);
      expect(mockCallback).toHaveBeenCalledWith({
        originalTotal: 120000,
        adjustedTotal: 100000,
        reductionFactor: expect.closeTo(0.8333, 3),
        adjustedAllocations: expect.any(Array),
      });
    });

    it('should not reduce when within limits', () => {
      const adjusted = validator.calculateProportionalReduction(mockAllocations, 100000);

      expect(adjusted).toEqual(mockAllocations);
    });
  });

  describe('Feedback Messages', () => {
    it('should generate success message for valid allocations', () => {
      const result = validator.validateAllocations(mockAllocations, 100000);
      const feedback = validator.generateFeedbackMessages(result);

      expect(feedback.successMessage).toBe('✅ All allocations are valid and well-balanced');
      expect(feedback.errorMessages).toHaveLength(0);
      expect(feedback.suggestions).toContain(
        'You have 40.0% of your patrimony unallocated. Consider diversifying further.'
      );
    });

    it('should generate error messages', () => {
      const invalidAllocations: AllocationItem[] = [
        {
          index: 0,
          category: 'Stocks',
          subcategory: 'Tech',
          value: -1000,
          percentage: 25,
          maxAllowed: 50,
        },
      ];
      const result = validator.validateAllocations(invalidAllocations, 100000);
      const feedback = validator.generateFeedbackMessages(result);

      expect(feedback.errorMessages).toContain('❌ Allocation 0: Value cannot be negative');
      expect(feedback.successMessage).toBeNull();
    });

    it('should generate warning messages', () => {
      const warningAllocations: AllocationItem[] = [
        {
          index: 0,
          category: 'Stocks',
          subcategory: '',
          value: 25000,
          percentage: 25,
          maxAllowed: 50,
        },
      ];
      const result = validator.validateAllocations(warningAllocations, 100000);
      const feedback = validator.generateFeedbackMessages(result);

      expect(feedback.warningMessages).toContain('⚠️ Allocation 0: Subcategory is recommended');
      expect(feedback.successMessage).toBe(
        '✅ Allocations are valid but have some recommendations'
      );
    });

    it('should suggest overflow reduction', () => {
      const result = validator.validateAllocations(mockAllocations, 50000);
      const feedback = validator.generateFeedbackMessages(result);

      expect(feedback.suggestions[0]).toContain('Consider reducing allocations');
      expect(feedback.suggestions[0]).toContain('10.000');
      expect(feedback.suggestions[0]).toContain('to stay within limits');
    });
  });

  describe('Duplicate Category Validation', () => {
    it('should detect duplicate categories', () => {
      const duplicateAllocations: AllocationItem[] = [
        mockAllocations[0]!,
        {
          index: 2,
          category: 'Stocks',
          subcategory: 'Tech',
          value: 15000,
          percentage: 15,
          maxAllowed: 50,
        },
      ];

      const result = validator.validateAllocations(duplicateAllocations, 100000);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(
        'Duplicate category/subcategory "Stocks-Tech" found in allocations: 0, 2'
      );
    });
  });

  describe('Risk Profile Validation', () => {
    it('should validate conservative risk profile', () => {
      const stockAllocations: AllocationItem[] = [
        {
          index: 0,
          category: 'Stocks',
          subcategory: 'Tech',
          value: 50000,
          percentage: 50,
          maxAllowed: 60,
        },
      ];

      const result = validator.validateRiskProfile(stockAllocations, 'conservative');

      expect(result.warnings).toContain(
        'Stock allocation (50.0%) exceeds conservative profile recommendation (40%)'
      );
    });

    it('should validate moderate risk profile', () => {
      const mixedAllocations: AllocationItem[] = [
        {
          index: 0,
          category: 'Stocks',
          subcategory: 'Tech',
          value: 60000,
          percentage: 60,
          maxAllowed: 70,
        },
        {
          index: 1,
          category: 'Bonds',
          subcategory: 'Gov',
          value: 30000,
          percentage: 30,
          maxAllowed: 40,
        },
      ];

      const result = validator.validateRiskProfile(mixedAllocations, 'moderate');

      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(0);
    });
  });

  describe('Patrimony State Validation', () => {
    it('should validate consistent patrimony state', () => {
      const state: PatrimonyState = {
        mainValue: 100000,
        totalAllocated: 60000,
        remaining: 40000,
        isFullyAllocated: false,
        isOverAllocated: false,
        percentageAllocated: 60,
      };

      const result = validator.validatePatrimonyState(state);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect negative main value', () => {
      const state: PatrimonyState = {
        mainValue: -100000,
        totalAllocated: 0,
        remaining: 0,
        isFullyAllocated: false,
        isOverAllocated: false,
        percentageAllocated: 0,
      };

      const result = validator.validatePatrimonyState(state);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Main value cannot be negative');
    });

    it('should detect inconsistent over-allocation flag', () => {
      const state: PatrimonyState = {
        mainValue: 100000,
        totalAllocated: 80000,
        remaining: 20000,
        isFullyAllocated: false,
        isOverAllocated: true, // Inconsistent
        percentageAllocated: 80,
      };

      const result = validator.validatePatrimonyState(state);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(
        'Over-allocation flag is set but total is not greater than main value'
      );
    });
  });

  describe('Configuration', () => {
    it('should update configuration', () => {
      validator.updateConfig({
        maxPercentage: 120,
        allowOverflow: true,
      });

      const config = validator.getConfig();
      expect(config.maxPercentage).toBe(120);
      expect(config.allowOverflow).toBe(true);
      expect(config.proportionalAdjustment).toBe(true); // Should keep existing value
    });

    it('should use custom configuration in validation', () => {
      validator.updateConfig({ maxPercentage: 120 });

      const result = validator.validateAllocations(mockAllocations, 50000); // 120% allocation

      expect(result.isValid).toBe(true); // Should pass with 120% limit
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty allocations array', () => {
      const result = validator.validateAllocations([], 100000);

      expect(result.isValid).toBe(true);
      expect(result.totalPercentage).toBe(0);
      expect(result.overflowAmount).toBe(0);
    });

    it('should handle zero main value', () => {
      const result = validator.validateAllocations(mockAllocations, 0);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Cannot allocate values when main patrimony value is zero');
    });
  });
});
