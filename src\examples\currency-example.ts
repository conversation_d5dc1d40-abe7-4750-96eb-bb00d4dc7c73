import { CurrencyController, CurrencyButtons } from '../modules/currency';

/**
 * Example usage of the Currency Management System
 * This demonstrates how to initialize currency inputs with formatting and controls
 */
export function initializeCurrencyExample(): void {
  // Initialize the currency controller
  const currencyController = new CurrencyController();
  const currencyButtons = new CurrencyButtons(currencyController);

  // Find all currency inputs in the page
  const currencyInputs = document.querySelectorAll(
    '[data-currency-input]'
  ) as NodeListOf<HTMLInputElement>;

  currencyInputs.forEach((input, index) => {
    // Configure input based on data attributes
    const config = {
      isMain: input.dataset.currencyMain === 'true',
      allowNegative: input.dataset.currencyNegative === 'true',
      minValue: input.dataset.currencyMin ? parseFloat(input.dataset.currencyMin) : undefined,
      maxValue: input.dataset.currencyMax ? parseFloat(input.dataset.currencyMax) : undefined,
      onValueChange: (value) => {
        console.log(`Currency input ${index} changed to:`, value);
      },
    };

    // Initialize the input with currency formatting
    currencyController.initializeInput(input, config);

    // Setup control buttons if they exist
    currencyButtons.setupButtons(input);
  });

  // Listen for currency changes globally
  currencyController.on('currencyChange', (event) => {
    console.log('Currency changed:', event);
  });

  // Listen specifically for main currency changes
  currencyController.on('mainCurrencyChange', (event) => {
    console.log('Main currency changed:', event);
    // Update other parts of the application based on main currency change
    updatePatrimonyCalculations(event.value);
  });
}

/**
 * Example function that would be called when main currency changes
 * @param mainValue New main currency value
 */
function updatePatrimonyCalculations(mainValue: number): void {
  // This would integrate with the patrimony system
  console.log('Updating patrimony calculations with main value:', mainValue);
}

/**
 * Example of programmatically setting currency values
 */
export function setCurrencyValues(): void {
  const currencyController = new CurrencyController();

  // Find main currency input
  const mainInput = document.querySelector('[data-currency-main="true"]') as HTMLInputElement;
  if (mainInput) {
    // Set a specific value
    currencyController.setValue(mainInput, 100000);
  }

  // Set values for all allocation inputs
  const allocationInputs = document.querySelectorAll(
    '[data-currency-allocation]'
  ) as NodeListOf<HTMLInputElement>;
  allocationInputs.forEach((input, index) => {
    const percentage = 0.2; // 20% each
    const allocationValue = 100000 * percentage;
    currencyController.setValue(input, allocationValue);
  });
}

/**
 * Example HTML structure that works with this system:
 *
 * <div class="currency-input-group">
 *   <input
 *     type="text"
 *     data-currency-input
 *     data-currency-main="true"
 *     data-currency-min="0"
 *     placeholder="R$ 0,00"
 *   />
 *   <button data-currency-decrement>-</button>
 *   <button data-currency-increment>+</button>
 * </div>
 *
 * <div class="currency-input-group">
 *   <input
 *     type="text"
 *     data-currency-input
 *     data-currency-allocation
 *     data-currency-min="0"
 *     placeholder="R$ 0,00"
 *   />
 *   <button data-currency-decrement>-</button>
 *   <button data-currency-increment>+</button>
 * </div>
 */
