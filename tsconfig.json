{"extends": "@finsweet/tsconfig", "compilerOptions": {"rootDir": ".", "baseUrl": "./", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "verbatimModuleSyntax": false, "paths": {"$utils/*": ["src/utils/*"], "$modules/*": ["src/modules/*"], "$types/*": ["src/types/*"], "$config/*": ["src/config/*"]}, "types": ["@finsweet/ts-utils", "jest", "node"]}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist"]}