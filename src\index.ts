/**
 * MODULARIZAÇÃO DO CÓDIGO WEBFLOW
 * Este arquivo inicializa exatamente o mesmo código que estava funcionando no Webflow,
 * apenas organizado em módulos TypeScript para manutenção
 */

import { WebflowSystemsInitializer } from './modules/webflow';

/**
 * Inicializa os sistemas extraídos do Webflow
 */
function initializeWebflowSystems(): void {
  // Aguarda o Webflow estar pronto e inicializa os sistemas
  window.Webflow ||= [];
  window.Webflow.push(() => {
    // Inicializa exatamente o mesmo código que estava funcionando
    WebflowSystemsInitializer.initialize();
  });
}

// Inicializa quando o DOM estiver pronto ou imediatamente se já estiver
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeWebflowSystems);
} else {
  initializeWebflowSystems();
}
