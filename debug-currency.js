const formatter = new Intl.NumberFormat('pt-BR', {
  style: 'currency',
  currency: 'BRL',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
});

const result = formatter.format(1234.56);
console.log('Result:', JSON.stringify(result));
console.log('Length:', result.length);
console.log(
  'Char codes:',
  [...result].map((c) => c.charCodeAt(0))
);

// Test negative
const negative = formatter.format(-1234.56);
console.log('Negative:', JSON.stringify(negative));
