import { CurrencyController } from '../../../src/modules/currency/CurrencyController';
import type { CurrencyChangeEvent } from '../../../src/modules/currency/types';

// Mock DOM environment
Object.defineProperty(global, 'setTimeout', {
  value: (callback: Function) => callback(),
});

describe('CurrencyController', () => {
  let controller: CurrencyController;
  let mockInput: HTMLInputElement;

  beforeEach(() => {
    controller = new CurrencyController();

    // Create mock input element
    mockInput = {
      type: 'text',
      value: '',
      dataset: {},
      addEventListener: jest.fn(),
      classList: {
        add: jest.fn(),
        remove: jest.fn(),
      },
      setSelectionRange: jest.fn(),
      select: jest.fn(),
      selectionStart: 0,
    } as any;
  });

  test('should initialize input correctly', () => {
    controller.initializeInput(mockInput);
    expect(mockInput.dataset.currencyInitialized).toBe('true');
  });

  test('should format initial value', () => {
    mockInput.value = '1234,56';
    controller.initializeInput(mockInput);
    expect(mockInput.value).toBe('R$ 1.234,56');
  });

  test('should get and set values correctly', () => {
    controller.initializeInput(mockInput);

    controller.setValue(mockInput, 1234.56);
    expect(mockInput.value).toBe('R$ 1.234,56');
    expect(controller.getValue(mockInput)).toBe(1234.56);
  });

  test('should validate values against constraints', () => {
    controller.initializeInput(mockInput, {
      minValue: 0,
      maxValue: 1000,
      allowNegative: false,
    });

    controller.setValue(mockInput, -100);
    expect(controller.getValue(mockInput)).toBe(0);

    controller.setValue(mockInput, 2000);
    expect(controller.getValue(mockInput)).toBe(1000);
  });

  test('should emit currency change events', (done) => {
    controller.initializeInput(mockInput);

    controller.on('currencyChange', (event: CurrencyChangeEvent) => {
      expect(event.value).toBe(1234.56);
      expect(event.formatted).toBe('R$ 1.234,56');
      done();
    });

    controller.setValue(mockInput, 1234.56);
  });

  test('should destroy input configuration', () => {
    controller.initializeInput(mockInput);
    expect(controller.getInitializedInputs()).toContain(mockInput);

    controller.destroy(mockInput);
    expect(controller.getInitializedInputs()).not.toContain(mockInput);
  });
});
