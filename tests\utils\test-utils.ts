// Test utilities for common testing patterns

/**
 * Create a mock HTML element with specified properties
 * @param tagName - HTML tag name
 * @param properties - Properties to set on the element
 * @returns Mock HTML element
 */
export function createMockElement<K extends keyof HTMLElementTagNameMap>(
  tagName: K,
  properties: Partial<HTMLElementTagNameMap[K]> = {}
): HTMLElementTagNameMap[K] {
  const element = document.createElement(tagName);
  Object.assign(element, properties);
  return element;
}

/**
 * Create a mock input element with value and dataset
 * @param value - Input value
 * @param dataset - Dataset properties
 * @returns Mock input element
 */
export function createMockInput(
  value: string = '',
  dataset: Record<string, string> = {}
): HTMLInputElement {
  const input = createMockElement('input', { value });
  Object.assign(input.dataset, dataset);
  return input;
}

/**
 * Create a mock div element with classes and dataset
 * @param classes - CSS classes
 * @param dataset - Dataset properties
 * @returns Mock div element
 */
export function createMockDiv(
  classes: string[] = [],
  dataset: Record<string, string> = {}
): HTMLDivElement {
  const div = createMockElement('div');
  div.className = classes.join(' ');
  Object.assign(div.dataset, dataset);
  return div;
}

/**
 * Wait for next tick (useful for async operations)
 * @returns Promise that resolves on next tick
 */
export function nextTick(): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, 0));
}

/**
 * Wait for specified number of animation frames
 * @param frames - Number of frames to wait
 * @returns Promise that resolves after frames
 */
export function waitFrames(frames: number = 1): Promise<void> {
  return new Promise((resolve) => {
    let count = 0;
    function frame() {
      count++;
      if (count >= frames) {
        resolve();
      } else {
        requestAnimationFrame(frame);
      }
    }
    requestAnimationFrame(frame);
  });
}

/**
 * Trigger a DOM event on an element
 * @param element - Target element
 * @param eventType - Event type
 * @param eventInit - Event initialization options
 */
export function triggerEvent<K extends keyof HTMLElementEventMap>(
  element: HTMLElement,
  eventType: K,
  eventInit?: EventInit
): void {
  const event = new Event(eventType, eventInit);
  element.dispatchEvent(event);
}

/**
 * Trigger a mouse event on an element
 * @param element - Target element
 * @param eventType - Mouse event type
 * @param eventInit - Mouse event initialization options
 */
export function triggerMouseEvent(
  element: HTMLElement,
  eventType: keyof HTMLElementEventMap,
  eventInit?: MouseEventInit
): void {
  const event = new MouseEvent(eventType, eventInit);
  element.dispatchEvent(event);
}

/**
 * Trigger a keyboard event on an element
 * @param element - Target element
 * @param eventType - Keyboard event type
 * @param eventInit - Keyboard event initialization options
 */
export function triggerKeyboardEvent(
  element: HTMLElement,
  eventType: keyof HTMLElementEventMap,
  eventInit?: KeyboardEventInit
): void {
  const event = new KeyboardEvent(eventType, eventInit);
  element.dispatchEvent(event);
}

/**
 * Mock fetch API response
 * @param data - Response data
 * @param status - HTTP status code
 * @param statusText - HTTP status text
 * @returns Mock fetch response
 */
export function mockFetchResponse(
  data: any,
  status: number = 200,
  statusText: string = 'OK'
): Response {
  return {
    ok: status >= 200 && status < 300,
    status,
    statusText,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
    headers: new Headers(),
    redirected: false,
    type: 'basic',
    url: '',
    clone: jest.fn(),
    body: null,
    bodyUsed: false,
    arrayBuffer: jest.fn(),
    blob: jest.fn(),
    formData: jest.fn(),
    bytes: jest.fn(),
  } as any as Response;
}

/**
 * Create a spy that can be used to track method calls
 * @param object - Object to spy on
 * @param method - Method name to spy on
 * @returns Jest spy
 */
export function createSpy<T extends object, K extends keyof T>(
  object: T,
  method: K
): jest.SpyInstance {
  return jest.spyOn(object, method as any);
}

/**
 * Assert that a function throws an error with specific message
 * @param fn - Function to test
 * @param expectedMessage - Expected error message
 */
export function expectToThrow(fn: () => void, expectedMessage?: string): void {
  expect(fn).toThrow(expectedMessage);
}

/**
 * Assert that an async function rejects with specific message
 * @param fn - Async function to test
 * @param expectedMessage - Expected error message
 */
export async function expectToReject(
  fn: () => Promise<any>,
  expectedMessage?: string
): Promise<void> {
  await expect(fn()).rejects.toThrow(expectedMessage);
}
