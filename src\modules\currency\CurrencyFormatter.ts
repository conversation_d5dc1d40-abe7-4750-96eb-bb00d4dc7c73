import currency from 'currency.js';

import type { CurrencyFormatterOptions, CurrencyValue } from './types';

/**
 * CurrencyFormatter class for handling Brazilian Real currency formatting and parsing
 * Implements singleton pattern for consistent formatting across the application
 * Uses currency.js library for precise currency operations
 */
export class CurrencyFormatter {
  private static instance: CurrencyFormatter;
  private locale: string = 'pt-BR';
  private currencyCode: string = 'BRL';
  private currencyConfig: currency.Options;

  private constructor(options?: CurrencyFormatterOptions) {
    this.locale = options?.locale || 'pt-BR';
    this.currencyCode = options?.currency || 'BRL';

    // Configure currency.js for Brazilian Real
    this.currencyConfig = {
      symbol: 'R$ ',
      precision: 2,
      separator: '.',
      decimal: ',',
    };
  }

  /**
   * Get singleton instance of CurrencyFormatter
   * @param options Optional configuration for the formatter
   * @returns CurrencyFormatter instance
   */
  static getInstance(options?: CurrencyFormatterOptions): CurrencyFormatter {
    if (!CurrencyFormatter.instance) {
      CurrencyFormatter.instance = new CurrencyFormatter(options);
    }
    return CurrencyFormatter.instance;
  }

  /**
   * Format a numeric value to Brazilian Real currency string
   * @param value Numeric value to format
   * @returns Formatted currency string (e.g., "R$ 1.234,56")
   */
  formatValue(value: number): string {
    if (isNaN(value) || !isFinite(value)) {
      return currency(0, this.currencyConfig).format();
    }
    return currency(value, this.currencyConfig).format();
  }

  /**
   * Parse a formatted currency string back to numeric value
   * @param formattedValue Formatted currency string (e.g., "R$ 1.234,56")
   * @returns Numeric value
   */
  parseValue(formattedValue: string): number {
    if (!formattedValue || typeof formattedValue !== 'string') {
      return 0;
    }

    // Remove currency symbol and clean the string
    const cleanValue = formattedValue.replace(/R\$\s?/, '');

    try {
      // Use currency.js to parse the Brazilian format
      const parsed = currency(cleanValue, {
        separator: '.',
        decimal: ',',
        precision: 2,
      });
      return parsed.value;
    } catch {
      return 0;
    }
  }

  /**
   * Format currency with explicit currency symbol
   * @param value Numeric value to format
   * @returns Formatted currency string
   */
  formatCurrency(value: number): string {
    return this.formatValue(value);
  }

  /**
   * Create a complete currency object with value and formatted string
   * @param value Numeric value
   * @returns CurrencyValue object
   */
  createCurrencyObject(value: number): CurrencyValue {
    const sanitizedValue = isNaN(value) || !isFinite(value) ? 0 : value;
    return {
      value: sanitizedValue,
      formatted: this.formatValue(sanitizedValue),
      currency: this.currencyCode,
    };
  }

  /**
   * Get the current locale being used
   * @returns Current locale string
   */
  getLocale(): string {
    return this.locale;
  }

  /**
   * Get the current currency code being used
   * @returns Current currency code
   */
  getCurrency(): string {
    return this.currencyCode;
  }

  /**
   * Validate if a string is a valid currency format
   * @param value String to validate
   * @returns True if valid currency format
   */
  isValidCurrencyFormat(value: string): boolean {
    const regex = /^R\$\s?[0-9]{1,3}(?:\.[0-9]{3})*(?:,[0-9]{2})?$/;
    return regex.test(value);
  }

  /**
   * Clean a string to extract only numeric characters for parsing
   * @param value Input string
   * @returns Cleaned string with only numbers, dots, and commas
   */
  cleanCurrencyString(value: string): string {
    // Remove everything except numbers, dots, and commas
    return value.replace(/[^\d.,]/g, '');
  }

  /**
   * Create a currency instance for mathematical operations
   * @param value Numeric value
   * @returns currency.js instance
   */
  createCurrencyInstance(value: number): currency {
    return currency(value, this.currencyConfig);
  }

  /**
   * Add two currency values
   * @param value1 First value
   * @param value2 Second value
   * @returns Sum as formatted currency string
   */
  add(value1: number, value2: number): string {
    const result = currency(value1, this.currencyConfig).add(value2);
    return result.format();
  }

  /**
   * Subtract two currency values
   * @param value1 First value
   * @param value2 Second value
   * @returns Difference as formatted currency string
   */
  subtract(value1: number, value2: number): string {
    const result = currency(value1, this.currencyConfig).subtract(value2);
    return result.format();
  }

  /**
   * Multiply currency value
   * @param value Base value
   * @param multiplier Multiplier
   * @returns Product as formatted currency string
   */
  multiply(value: number, multiplier: number): string {
    const result = currency(value, this.currencyConfig).multiply(multiplier);
    return result.format();
  }

  /**
   * Divide currency value
   * @param value Base value
   * @param divisor Divisor
   * @returns Quotient as formatted currency string
   */
  divide(value: number, divisor: number): string {
    const result = currency(value, this.currencyConfig).divide(divisor);
    return result.format();
  }
}
