<!DOCTYPE html><!--  This site was created in Webflow. https://webflow.com  --><!--  Last Published: Mon Jul 28 2025 18:19:55 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="688144ef794cddf39f7c43af" data-wf-site="688144ef794cddf39f7c4396">
<head>
  <meta charset="utf-8">
  <title>Grupo&#x27;s Groovy Site</title>
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <meta content="Webflow" name="generator">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/grupos-groovy-site-fc802f.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">
      ! function(o, c) {
        var n = c.documentElement,
          t = " w-mod-";
        n.className += t + "js", ("ontouchstart" in o || o.DocumentTouch && c instanceof DocumentTouch) && (n.className += t + "touch")
      }(window, document);
    </script>
  <link href="images/favicon.ico" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/range-slider-element@2/dist/range-slider-element.css">
  <link href="http://localhost:3000/index.css" rel="stylesheet" type="text/css">
  <script defer="" src="http://localhost:3000/index.js"></script>
  <style>
      .active-produto-item {
        display: none;
        opacity: 0;
      }
      .texto-item {
        mix-blend-mode: difference !important;
      }
    </style>
</head>
<body>
  <div class="slider-resultado hide">
    <div class="global-styles-2"></div>
    <main class="main-wrapper-2">
      <section class="section">
        <div class="container">
          <div class="chart_wrap">
            <div end="75" start="25" class="chart_column">
              <div class="chart_bar-wrap">
                <div class="chart_bar"></div>
                <div class="chart_bar-key">
                  <p class="chart_bar-key-text"><span class="chart_number">0</span>%</p>
                </div>
              </div>
              <p class="chart_column-title">Happiness</p>
            </div>
            <div end="35" start="86" class="chart_column">
              <div class="chart_bar-wrap">
                <div class="chart_bar"></div>
                <div class="chart_bar-key">
                  <p class="chart_bar-key-text"><span class="chart_number">0</span>%</p>
                </div>
              </div>
              <p class="chart_column-title">Safety</p>
            </div>
            <div end="79" start="41" class="chart_column">
              <div class="chart_bar-wrap">
                <div class="chart_bar"></div>
                <div class="chart_bar-key">
                  <p class="chart_bar-key-text"><span class="chart_number">0</span>%</p>
                </div>
              </div>
              <p class="chart_column-title">Income</p>
            </div>
            <div end="91" start="27" class="chart_column">
              <div class="chart_bar-wrap">
                <div class="chart_bar"></div>
                <div class="chart_bar-key">
                  <p class="chart_bar-key-text"><span class="chart_number">0</span>%</p>
                </div>
              </div>
              <p class="chart_column-title">Free Time</p>
            </div>
            <div end="39" start="76" class="chart_column">
              <div class="chart_bar-wrap">
                <div class="chart_bar"></div>
                <div class="chart_bar-key">
                  <p class="chart_bar-key-text"><span class="chart_number">0</span>%</p>
                </div>
              </div>
              <p class="chart_column-title">Stability</p>
            </div>
            <div end="98" start="38" class="chart_column">
              <div class="chart_bar-wrap">
                <div class="chart_bar"></div>
                <div class="chart_bar-key">
                  <p class="chart_bar-key-text"><span class="chart_number">0</span>%</p>
                </div>
              </div>
              <p class="chart_column-title">Flexibility </p>
            </div>
            <div end="45" start="68" class="chart_column">
              <div class="chart_bar-wrap">
                <div class="chart_bar"></div>
                <div class="chart_bar-key">
                  <p class="chart_bar-key-text"><span class="chart_number">0</span>%</p>
                </div>
              </div>
              <p class="chart_column-title">Socialization</p>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
  <div class="page-wrapper calc-variation">
    <div class="range-slider-css w-embed">
      <style>
          range-slider {
            --track-size: 0.5rem;
            --thumb-size: 1.0rem;
            &:not([orientation="vertical"]) {
              inline-size: 100%;
            }
            [data-track] {
              background: #f4f3f1;
              border-radius: 100vw;
            }
            [data-track-fill] {
              background: #000000;
              border-radius: 100vw;
            }
            [data-thumb] {
              background: #000000;
              border: none;
              border-radius: 100vw;
              cursor: pointer;
              transition: transform 200ms ease;
              will-change: transform;
            }
            [data-thumb]:active {
              box-shadow: none;
              transform: scale(1.1);
            }
            [data-thumb]:focus {
              outline: none;
            }
          }
        </style>
    </div>
    <div class="global-styles w-embed">
      <style>
          body {
            font-size: 1.1111111111111112vw;
          }
          /* Max Font Size */
          @media screen and (min-width:1920px) {
            body {
              font-size: 21.333333333333332px;
            }
          }
          /* Container Max Width */
          .container {
            max-width: 1920px;
          }
          /* Min Font Size */
          @media screen and (max-width:991px) {
            body {
              font-size: 11.011111111111111px;
            }
          }
          /* Make text look crisper and more legible in all browsers */
          body {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
          }
          /* Focus state style for keyboard navigation for the focusable elements */
          *[tabindex]:focus-visible,
          input[type="file"]:focus-visible {
            outline: 0.125rem solid #4d65ff;
            outline-offset: 0.125rem;
          }
          /* Set color style to inherit */
          .inherit-color * {
            color: inherit;
          }
          /* Get rid of top margin on first element in any rich text element */
          .w-richtext> :not(div):first-child,
          .w-richtext>div:first-child> :first-child {
            margin-top: 0 !important;
          }
          /* Get rid of bottom margin on last element in any rich text element */
          .w-richtext>:last-child,
          .w-richtext ol li:last-child,
          .w-richtext ul li:last-child {
            margin-bottom: 0 !important;
          }
          /* Make sure containers never lose their center alignment */
          .container-medium,
          .container-small,
          .container-large {
            margin-right: auto !important;
            margin-left: auto !important;
          }
          /* 
Make the following elements inherit typography styles from the parent and not have hardcoded values. 
Important: You will not be able to style for example "All Links" in Designer with this CSS applied.
Uncomment this CSS to use it in the project. Leave this message for future hand-off.
*/
          /*
a,
.w-input,
.w-select,
.w-tab-link,
.w-nav-link,
.w-dropdown-btn,
.w-dropdown-toggle,
.w-dropdown-link {
  color: inherit;
  text-decoration: inherit;
  font-size: inherit;
}
*/
          /* Apply "..." after 3 lines of text */
          .text-style-3lines {
            display: -webkit-box;
            overflow: hidden;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }
          /* Apply "..." after 2 lines of text */
          .text-style-2lines {
            display: -webkit-box;
            overflow: hidden;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          /* These classes are never overwritten */
          .hide {
            display: none !important;
          }
          @media screen and (max-width: 991px) {
            .hide,
            .hide-tablet {
              display: none !important;
            }
          }
          @media screen and (max-width: 767px) {
            .hide-mobile-landscape {
              display: none !important;
            }
          }
          @media screen and (max-width: 479px) {
            .hide-mobile {
              display: none !important;
            }
          }
          .margin-0 {
            margin: 0rem !important;
          }
          .padding-0 {
            padding: 0rem !important;
          }
          .spacing-clean {
            padding: 0rem !important;
            margin: 0rem !important;
          }
          .margin-top {
            margin-right: 0rem !important;
            margin-bottom: 0rem !important;
            margin-left: 0rem !important;
          }
          .padding-top {
            padding-right: 0rem !important;
            padding-bottom: 0rem !important;
            padding-left: 0rem !important;
          }
          .margin-right {
            margin-top: 0rem !important;
            margin-bottom: 0rem !important;
            margin-left: 0rem !important;
          }
          .padding-right {
            padding-top: 0rem !important;
            padding-bottom: 0rem !important;
            padding-left: 0rem !important;
          }
          .margin-bottom {
            margin-top: 0rem !important;
            margin-right: 0rem !important;
            margin-left: 0rem !important;
          }
          .padding-bottom {
            padding-top: 0rem !important;
            padding-right: 0rem !important;
            padding-left: 0rem !important;
          }
          .margin-left {
            margin-top: 0rem !important;
            margin-right: 0rem !important;
            margin-bottom: 0rem !important;
          }
          .padding-left {
            padding-top: 0rem !important;
            padding-right: 0rem !important;
            padding-bottom: 0rem !important;
          }
          .margin-horizontal {
            margin-top: 0rem !important;
            margin-bottom: 0rem !important;
          }
          .padding-horizontal {
            padding-top: 0rem !important;
            padding-bottom: 0rem !important;
          }
          .margin-vertical {
            margin-right: 0rem !important;
            margin-left: 0rem !important;
          }
          .padding-vertical {
            padding-right: 0rem !important;
            padding-left: 0rem !important;
          }
          a {
            color: inherit;
          }
          @keyframes piscar {
            0% {
              opacity: 1;
            }
            50% {
              opacity: 0;
            }
            100% {
              opacity: 1;
            }
          }
          .dot_transp {
            animation: piscar 1s infinite;
          }
        </style>
    </div>
    <div class="style-grid-patrimonio w-embed">
      <style>
          /* Estratégia otimizada para telas desktop (991px+) */
          .patrimonio_interactive_content-wrapper {
            overflow: hidden;
            border-radius: 30px;
            container-type: inline-size;
          }
          .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item {
            border: none;
            border-radius: 0;
            position: relative;
          }
          /* Bordas usando pseudo-elementos para maior flexibilidade */
          .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item::before,
          .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item::after {
            content: '';
            position: absolute;
            background-color: rgba(0, 0, 0, 0.24);
            z-index: 1;
          }
          /* Borda direita */
          .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item::before {
            top: 0;
            right: 0;
            width: 1px;
            height: 100%;
          }
          /* Borda inferior */
          .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item::after {
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
          }
          /* Layout específico para desktop (991px+) - 4 colunas */
          @media screen and (min-width: 991px) {
            .patrimonio_interactive_content-wrapper {
              display: grid;
              grid-template-columns: repeat(4, 1fr);
            }
            /* Cantos arredondados para 4 colunas */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:first-child {
              border-top-left-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-child(4) {
              border-top-right-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-last-child(4) {
              border-bottom-left-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:last-child {
              border-bottom-right-radius: 30px;
            }
            /* Remove borda direita da última coluna (a cada 4 itens) */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-child(4n)::before {
              display: none;
            }
            /* Remove borda inferior da última linha (últimos 4 itens) */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-last-child(-n+4)::after {
              display: none;
            }
          }
          /* Tablet horizontal (768px - 990px) - 2 colunas */
          @media screen and (max-width: 990px) and (min-width: 768px) {
            .patrimonio_interactive_content-wrapper {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
            }
            /* Cantos arredondados para 2 colunas */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:first-child {
              border-top-left-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-child(2) {
              border-top-right-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-last-child(2) {
              border-bottom-left-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:last-child {
              border-bottom-right-radius: 30px;
            }
            /* Remove borda direita da segunda coluna */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-child(2n)::before {
              display: none;
            }
            /* Remove borda inferior da última linha */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-last-child(-n+2)::after {
              display: none;
            }
          }
          /* Tablet vertical (521px - 767px) - 2 colunas */
          @media screen and (max-width: 767px) and (min-width: 521px) {
            .patrimonio_interactive_content-wrapper {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
            }
            /* Cantos arredondados para 2 colunas */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:first-child {
              border-top-left-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-child(2) {
              border-top-right-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-last-child(2) {
              border-bottom-left-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:last-child {
              border-bottom-right-radius: 30px;
            }
            /* Remove borda direita da segunda coluna */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-child(2n)::before {
              display: none;
            }
            /* Remove borda inferior da última linha */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:nth-last-child(-n+2)::after {
              display: none;
            }
          }
          /* Mobile (até 520px) - 1 coluna */
          @media screen and (max-width: 520px) {
            .patrimonio_interactive_content-wrapper {
              display: grid;
              grid-template-columns: 1fr;
            }
            /* Cantos arredondados para 1 coluna */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:first-child {
              border-top-left-radius: 30px;
              border-top-right-radius: 30px;
            }
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:last-child {
              border-bottom-left-radius: 30px;
              border-bottom-right-radius: 30px;
            }
            /* Remove todas as bordas direitas no mobile */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item::before {
              display: none;
            }
            /* Remove borda inferior apenas do último item */
            .patrimonio_interactive_content-wrapper>.patrimonio_interactive_item:last-child::after {
              display: none;
            }
          }
        </style>
    </div><button class="main-site-reino">
      <div class="icon-1x1 w-embed"><svg width="18" height="13" viewbox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16.0048 1.9416C16.0755 1.90906 16.1109 1.84539 16.1109 1.75012C16.1109 1.64684 16.0769 1.57751 16.0081 1.54214C15.9699 1.52186 15.9118 1.51196 15.835 1.51196H15.4214V1.96942H15.8246C15.9048 1.96942 15.9647 1.95999 16.0048 1.9416ZM15.7888 2.25097H15.4214V2.91878H15.0738V1.21626H15.909C16.0283 1.21862 16.1198 1.23324 16.1844 1.26012C16.2486 1.287 16.3033 1.32709 16.3476 1.37896C16.3849 1.42235 16.4141 1.46999 16.4353 1.52233C16.457 1.57468 16.4679 1.63458 16.4679 1.70155C16.4679 1.78219 16.4476 1.8619 16.4066 1.93971C16.3655 2.018 16.2986 2.07318 16.2047 2.10572C16.283 2.13732 16.3387 2.18212 16.3712 2.24013C16.4042 2.29814 16.4207 2.3868 16.4207 2.50612V2.62072C16.4207 2.69853 16.4235 2.75135 16.4297 2.77871C16.4391 2.82257 16.4603 2.85511 16.4943 2.87586V2.91878H16.1028C16.092 2.88105 16.0845 2.85039 16.0797 2.82728C16.0708 2.77965 16.0656 2.7306 16.0646 2.68061L16.0623 2.52262C16.0609 2.41415 16.0411 2.34152 16.0033 2.30521C15.9647 2.26937 15.8935 2.25097 15.7888 2.25097Z" fill="black"></path>
          <path d="M15.7841 0.755303C15.0607 0.755303 14.4716 1.34435 14.4716 2.0678C14.4716 2.79125 15.0607 3.38029 15.7841 3.38029C16.5076 3.38029 17.0966 2.79125 17.0966 2.0678C17.0966 1.34435 16.5076 0.755303 15.7841 0.755303ZM15.7841 3.8519C14.8003 3.8519 14 3.05158 14 2.0678C14 1.08402 14.8003 0.283691 15.7841 0.283691C16.7679 0.283691 17.5682 1.08402 17.5682 2.0678C17.5682 3.05158 16.7679 3.8519 15.7841 3.8519Z" fill="black"></path>
          <path d="M0 5.75911C0.97763 5.75911 1.91245 5.76029 2.84726 5.75833C3.05465 5.75793 3.04522 5.89658 3.04483 6.03916C3.04405 6.88089 3.04444 7.72262 3.04444 8.56474C3.04444 9.94143 3.04444 11.3185 3.04444 12.7164H0V5.75911Z" fill="black"></path>
          <path d="M0.0183908 2.43102V0.283691H12.4609C12.4554 0.409381 12.4452 0.535071 12.4452 0.661154C12.444 2.70322 12.4444 4.74528 12.4444 6.78735V7.17581C11.5052 7.17581 10.6117 7.16716 9.7181 7.1813C9.46475 7.18562 9.39759 7.10746 9.40073 6.85844C9.41408 5.76415 9.40034 4.66908 9.41212 3.5744C9.41487 3.32499 9.33946 3.248 9.08847 3.25075C7.90974 3.26254 6.73061 3.25547 5.55188 3.25665C5.48314 3.25665 5.4144 3.26764 5.28361 3.27903C5.37591 3.39019 5.42697 3.46285 5.48864 3.52452C7.68389 5.72016 9.8756 7.91895 12.0815 10.1036C12.3489 10.3679 12.4778 10.6248 12.4534 11.007C12.4181 11.5691 12.4444 12.135 12.4444 12.7156C11.7476 12.7156 11.096 12.7195 10.4447 12.7105C10.3646 12.7097 10.2668 12.6594 10.2083 12.6009C8.11516 10.5113 6.02753 8.41699 3.93519 6.32661C2.63274 5.02533 1.32438 3.72955 0.0183908 2.43102Z" fill="black"></path>
        </svg></div>
      <div class="text-item">Conheça a Reino Capital</div>
    </button>
    <div class="main-wrapper calc-variation">
      <div class="top-header-navbar">
        <div class="reino-logo-back">
          <div class="back-button hide">
            <div class="icon-1x1 w-embed"><svg width="10" height="16" viewbox="0 0 10 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.33366 1.3335L1.66699 8.00016L8.33366 14.6668" stroke="#323232" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg></div>
            <div>Voltar</div>
          </div>
          <a href="#" class="logo-reino w-inline-block"><img src="images/Reino---Logo.svg" loading="lazy" alt=""></a>
        </div><button class="ajuda-button hide-mobile-portrait">
          <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 17.8125C14.3147 17.8125 17.8125 14.3147 17.8125 10C17.8125 5.68528 14.3147 2.1875 10 2.1875C5.68528 2.1875 2.1875 5.68528 2.1875 10C2.1875 14.3147 5.68528 17.8125 10 17.8125Z" stroke="black" stroke-opacity="0.28" stroke-width="1.875" stroke-linecap="round" stroke-linejoin="round"></path>
              <path d="M10 10V13.75" stroke="black" stroke-opacity="0.28" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"></path>
              <path d="M10.625 6.25C10.625 6.41576 10.5592 6.57473 10.4419 6.69194C10.3247 6.80915 10.1658 6.875 10 6.875C9.83424 6.875 9.67527 6.80915 9.55806 6.69194C9.44085 6.57473 9.375 6.41576 9.375 6.25C9.375 6.08424 9.44085 5.92527 9.55806 5.80806C9.67527 5.69085 9.83424 5.625 10 5.625C10.1658 5.625 10.3247 5.69085 10.4419 5.80806C10.5592 5.92527 10.625 6.08424 10.625 6.25Z" stroke="black" stroke-opacity="0.28" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg></div>
          <div class="text-item">Para que serve esse app?</div>
        </button>
      </div>
      <section class="_0-home-section-calc-intro">
        <div class="padding-espec">
          <div class="container-espec">
            <div class="intro_content-wrapper">
              <div class="intro_text-wrapper">
                <div class="intro-top-text">
                  <div class="text-hat">
                    <div class="text-block">Um produto da <span class="text-weight-bold">Reino Capital</span></div>
                    <div class="icon-1x1 w-embed"><svg width="18" height="13" viewbox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.0048 1.9416C16.0755 1.90906 16.1109 1.84539 16.1109 1.75012C16.1109 1.64684 16.0769 1.57751 16.0081 1.54214C15.9699 1.52186 15.9118 1.51196 15.835 1.51196H15.4214V1.96942H15.8246C15.9048 1.96942 15.9647 1.95999 16.0048 1.9416ZM15.7888 2.25097H15.4214V2.91878H15.0738V1.21626H15.909C16.0283 1.21862 16.1198 1.23324 16.1844 1.26012C16.2486 1.287 16.3033 1.32709 16.3476 1.37896C16.3849 1.42235 16.4141 1.46999 16.4353 1.52233C16.457 1.57468 16.4679 1.63458 16.4679 1.70155C16.4679 1.78219 16.4476 1.8619 16.4066 1.93971C16.3655 2.018 16.2986 2.07318 16.2047 2.10572C16.283 2.13732 16.3387 2.18212 16.3712 2.24013C16.4042 2.29814 16.4207 2.3868 16.4207 2.50612V2.62072C16.4207 2.69853 16.4235 2.75135 16.4297 2.77871C16.4391 2.82257 16.4603 2.85511 16.4943 2.87586V2.91878H16.1028C16.092 2.88105 16.0845 2.85039 16.0797 2.82728C16.0708 2.77965 16.0656 2.7306 16.0646 2.68061L16.0623 2.52262C16.0609 2.41415 16.0411 2.34152 16.0033 2.30521C15.9647 2.26937 15.8935 2.25097 15.7888 2.25097Z" fill="black"></path>
                        <path d="M15.7841 0.755303C15.0607 0.755303 14.4716 1.34435 14.4716 2.0678C14.4716 2.79125 15.0607 3.38029 15.7841 3.38029C16.5076 3.38029 17.0966 2.79125 17.0966 2.0678C17.0966 1.34435 16.5076 0.755303 15.7841 0.755303ZM15.7841 3.8519C14.8003 3.8519 14 3.05158 14 2.0678C14 1.08402 14.8003 0.283691 15.7841 0.283691C16.7679 0.283691 17.5682 1.08402 17.5682 2.0678C17.5682 3.05158 16.7679 3.8519 15.7841 3.8519Z" fill="black"></path>
                        <path d="M0 5.75911C0.97763 5.75911 1.91245 5.76029 2.84726 5.75833C3.05465 5.75793 3.04522 5.89658 3.04483 6.03916C3.04405 6.88089 3.04444 7.72262 3.04444 8.56474C3.04444 9.94143 3.04444 11.3185 3.04444 12.7164H0V5.75911Z" fill="black"></path>
                        <path d="M0.0183908 2.43102V0.283691H12.4609C12.4554 0.409381 12.4452 0.535071 12.4452 0.661154C12.444 2.70322 12.4444 4.74528 12.4444 6.78735V7.17581C11.5052 7.17581 10.6117 7.16716 9.7181 7.1813C9.46475 7.18562 9.39759 7.10746 9.40073 6.85844C9.41408 5.76415 9.40034 4.66908 9.41212 3.5744C9.41487 3.32499 9.33946 3.248 9.08847 3.25075C7.90974 3.26254 6.73061 3.25547 5.55188 3.25665C5.48314 3.25665 5.4144 3.26764 5.28361 3.27903C5.37591 3.39019 5.42697 3.46285 5.48864 3.52452C7.68389 5.72016 9.8756 7.91895 12.0815 10.1036C12.3489 10.3679 12.4778 10.6248 12.4534 11.007C12.4181 11.5691 12.4444 12.135 12.4444 12.7156C11.7476 12.7156 11.096 12.7195 10.4447 12.7105C10.3646 12.7097 10.2668 12.6594 10.2083 12.6009C8.11516 10.5113 6.02753 8.41699 3.93519 6.32661C2.63274 5.02533 1.32438 3.72955 0.0183908 2.43102Z" fill="black"></path>
                      </svg></div>
                  </div>
                  <h1 class="heading-style-h1-em">Gerencie seus ativos e descubra o quanto de taxa você gasta com seu gestor</h1>
                </div>
                <div class="interactive-cards-wrapper">
                  <div class="interactive-cards-item">
                    <div class="interactive-main-item"></div>
                    <div class="interactive-etapa">
                      <div class="number-interactive">1</div>
                      <div class="text-item">Defina seu patrimônio</div>
                    </div>
                  </div>
                  <div class="interactive-cards-item">
                    <div class="interactive-main-item"></div>
                    <div class="interactive-etapa">
                      <div class="number-interactive">2</div>
                      <div class="text-item">Escolha seus ativos</div>
                    </div>
                  </div>
                  <div class="interactive-cards-item">
                    <div class="interactive-main-item"></div>
                    <div class="interactive-etapa">
                      <div class="number-interactive">3</div>
                      <div class="text-item">Aloque seu patrimônio</div>
                    </div>
                  </div>
                  <div id="w-node-_465990d1-5f58-426f-4cb3-920c1690ac11-9f7c43af" class="interactive-cards-item">
                    <div class="interactive-main-item"></div>
                    <div class="interactive-etapa">
                      <div class="number-interactive">4</div>
                      <div class="text-item">Veja o resultado</div>
                    </div>
                  </div>
                </div>
                <div class="button-and-info"><button data-w-id="28488048-b97f-492d-10e0-f6faa1572951" class="button-hero variation">
                    <div>Vamos começar</div>
                    <div class="arrow-effect">
                      <div class="arrow is-2">→</div>
                      <div class="arrow">→</div>
                    </div>
                  </button>
                  <div class="text-size-small">Leva menos de ~4 minutos</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="_1-section-calc-money">
        <div class="padding-espec">
          <div class="container-espec">
            <div class="section_form">
              <div class="money_content-wrapper">
                <div class="money_content_left-wrapper">
                  <div class="text-size-medium text-weight-medium">1 / 4</div>
                  <div class="heading-and-paragraph _22">
                    <h2 class="heading-style-h2-em width-default">Qual o seu patrimônio atual?</h2>
                    <p class="text-size-large-em width-default text-weight-medium text-style-muted">O valor real ou uma estimativa. Vamos precisar para calcular os seus ativos nas próximas tela.</p>
                  </div><button data-w-id="28488048-b97f-492d-10e0-f6faa157296b" class="button-hero variation money hide-tablet">
                    <div>Próximo</div>
                    <div class="arrow-effect">
                      <div class="arrow is-2">→</div>
                      <div class="arrow">→</div>
                    </div>
                  </button>
                </div>
                <div class="money_content_right-wrapper">
                  <div class="input_currency-wrapper"><input id="currency" placeholder="0,00" data-currency="true" is-main="true" class="currency-input">
                    <div class="brl_tag">R$</div>
                    <div class="interative-arrow w-embed"><svg id="interative-arrow" width="22" height="27" viewbox="0 0 22 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.0044 1.00684V25.9988M11.0044 1.00684L1.00586 11.0036M11.0044 1.00684L21.0001 11.0036" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </div>
                  <div class="currency_buttons-wrapper"><button currency-control="decrease" class="button-decrease">
                      <div class="icon-decrease_asset w-embed"><svg width="48" height="8" viewbox="0 0 48 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M45.6713 4.15832H2.64453" stroke="currentColor" stroke-width="4.08753" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg></div>
                    </button><button currency-control="increase" class="button-increase">
                      <div class="icon_increase-asset w-embed"><svg width="39" height="39" viewbox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M36.561 19.1586H1.75684" stroke="currentColor" stroke-width="3.48042" stroke-linecap="round" stroke-linejoin="round"></path>
                          <path d="M19.1582 36.56V1.75586" stroke="currentColor" stroke-width="3.48042" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg></div>
                    </button></div>
                </div>
              </div>
              <div class="next-content vv hide-tablet">
                <div class="number-session">
                  <div class="text-size-medium text-weight-medium session-number">2 / 5</div>
                </div>
                <div class="text-size-medium text-weight-medium">Você sabe onde o seu patrimônio está investido?</div>
                <div class="text-size-regular text-weight-medium text-style-muted">Escolha entre tipos de ativos.</div>
              </div>
              <div class="codigo-controle-secao-currency w-embed w-script">
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                      const input = document.querySelector('[is-main="true"]');
                      if (!input) return;
                      // Função para calcular incremento inteligente
                      const getIncrement = (value) => value < 1000 ? 100 : value < 10000 ? 1000 : value < 100000 ? 10000 : value < 1000000 ? 50000 : 100000;
                      // Função para atualizar valor
                      const updateValue = (newValue) => {
                        input.value = new Intl.NumberFormat('pt-BR', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }).format(newValue);
                        input.dispatchEvent(new Event('input', {
                          bubbles: true
                        }));
                      };
                      // Decrease
                      document.querySelectorAll('[currency-control="decrease"]').forEach(btn => {
                        btn.addEventListener('click', e => {
                          e.preventDefault();
                          const current = parseFloat(input.value.replace(/[^\d,]/g, '').replace(',', '.')) || 0;
                          updateValue(Math.max(0, current - getIncrement(current)));
                        });
                      });
                      // Increase
                      document.querySelectorAll('[currency-control="increase"]').forEach(btn => {
                        btn.addEventListener('click', e => {
                          e.preventDefault();
                          const current = parseFloat(input.value.replace(/[^\d,]/g, '').replace(',', '.')) || 0;
                          updateValue(current + getIncrement(current));
                        });
                      });
                    });
                  </script>
              </div>
              <div class="codigo-formatacao-currency w-embed w-script">
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                      // Aguarda o Webflow carregar completamente
                      window.Webflow && window.Webflow.push(function() {
                        initializeCurrencySystem();
                      });
                      // Fallback caso o Webflow não esteja disponível
                      setTimeout(initializeCurrencySystem, 100);
                    });
                    function initializeCurrencySystem() {
                      // Configuração para Real Brasileiro
                      const formatBRL = (value) => {
                        return new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(value);
                      };
                      // Função para formatar input enquanto digita
                      const formatCurrencyInput = (input) => {
                        let value = input.value.replace(/\D/g, '');
                        if (value === '') {
                          input.value = '';
                          return 0;
                        }
                        // Converte centavos para reais
                        const numericValue = parseInt(value) / 100;
                        // Formata usando Intl.NumberFormat
                        const formatted = new Intl.NumberFormat('pt-BR', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }).format(numericValue);
                        input.value = formatted;
                        return numericValue;
                      };
                      // Função para obter valor numérico limpo
                      const getCurrencyValue = (input) => {
                        const cleanValue = input.value.replace(/[^\d,]/g, '').replace(',', '.');
                        return parseFloat(cleanValue) || 0;
                      };
                      // Inicializa todos os inputs com data-currency
                      const currencyInputs = document.querySelectorAll('[data-currency="true"]');
                      currencyInputs.forEach(input => {
                        if (!input) return; // Proteção contra null
                        // Remove listeners existentes para evitar duplicação
                        input.removeEventListener('input', handleCurrencyInput);
                        input.removeEventListener('focus', handleCurrencyFocus);
                        input.removeEventListener('blur', handleCurrencyBlur);
                        // Adiciona novos listeners
                        input.addEventListener('input', handleCurrencyInput);
                        input.addEventListener('focus', handleCurrencyFocus);
                        input.addEventListener('blur', handleCurrencyBlur);
                        // Formatação inicial se já houver valor
                        if (input.value && input.value !== input.placeholder) {
                          formatCurrencyInput(input);
                        }
                      });
                      function handleCurrencyInput(e) {
                        const numericValue = formatCurrencyInput(e.target);
                        // Dispara evento customizado para outros scripts
                        e.target.dispatchEvent(new CustomEvent('currencyChange', {
                          detail: {
                            value: numericValue,
                            currencyValue: currency(numericValue),
                            formatted: formatBRL(numericValue)
                          }
                        }));
                      }
                      function handleCurrencyFocus(e) {
                        // Remove formatação para edição mais fácil
                        const value = getCurrencyValue(e.target);
                        if (value > 0) {
                          e.target.value = value.toFixed(2).replace('.', ',');
                        }
                      }
                      function handleCurrencyBlur(e) {
                        // Reaplica formatação completa
                        formatCurrencyInput(e.target);
                      }
                      // Função para calcular com precisão usando Currency.js
                      window.calculateCurrency = function(value1, value2, operation = 'add') {
                        const curr1 = currency(value1);
                        const curr2 = currency(value2);
                        switch (operation) {
                          case 'add':
                            return curr1.add(curr2);
                          case 'subtract':
                            return curr1.subtract(curr2);
                          case 'multiply':
                            return curr1.multiply(curr2);
                          case 'divide':
                            return curr1.divide(curr2);
                          default:
                            return curr1;
                        }
                      };
                      // Função para formatar qualquer valor
                      window.formatCurrency = formatBRL;
                      // Exemplo de uso com o input principal do patrimônio
                      const mainCurrencyInput = document.querySelector('#currency');
                      if (mainCurrencyInput) {
                        mainCurrencyInput.addEventListener('currencyChange', function(e) {
                          console.log('Valor alterado:', e.detail);
                          // Exemplo: calcular taxa de 2%
                          const withFee = window.calculateCurrency(e.detail.value, 0.02, 'multiply');
                          console.log('Com taxa:', window.formatCurrency(withFee.value));
                        });
                      }
                      // Para inputs individuais de alocação
                      const individualInputs = document.querySelectorAll('.currency-input.individual');
                      individualInputs.forEach(input => {
                        if (!input) return;
                        input.addEventListener('currencyChange', function(e) {
                          // Aqui você pode implementar lógica de soma total, validações, etc.
                          updateTotalAllocation();
                        });
                      });
                      function updateTotalAllocation() {
                        let total = currency(0);
                        document.querySelectorAll('.currency-input.individual').forEach(input => {
                          if (input && input.value) {
                            const value = getCurrencyValue(input);
                            total = total.add(value);
                          }
                        });
                        console.log('Total alocado:', window.formatCurrency(total.value));
                        // Dispara evento para outros componentes
                        document.dispatchEvent(new CustomEvent('totalAllocationChange', {
                          detail: {
                            total: total.value,
                            formatted: window.formatCurrency(total.value)
                          }
                        }));
                      }
                      console.log('Sistema de moeda inicializado com Currency.js + Intl.NumberFormat');
                    }
                    // Reinicializa após mudanças no Webflow (útil para elementos dinâmicos)
                    if (window.Webflow) {
                      window.Webflow.push(function() {
                        // Observa mudanças no DOM para reinicializar inputs dinâmicos
                        const observer = new MutationObserver(function(mutations) {
                          mutations.forEach(function(mutation) {
                            if (mutation.addedNodes.length) {
                              setTimeout(initializeCurrencySystem, 50);
                            }
                          });
                        });
                        observer.observe(document.body, {
                          childList: true,
                          subtree: true
                        });
                      });
                    }
                  </script>
              </div>
              <div class="animacao-botao w-embed w-script"><!--  FOOTER CODE - Corrigido para estrutura real  -->
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                      const waitForMotion = () => {
                        if (window.Motion) {
                          initMotionEffects();
                        } else {
                          setTimeout(waitForMotion, 50);
                        }
                      };
                      waitForMotion();
                      function initMotionEffects() {
                        const {
                          animate,
                          hover,
                          press
                        } = window.Motion;
                        // ========== BUSCA ELEMENTOS PELA ESTRUTURA REAL ==========
                        const input = document.querySelector('input[is-main="true"]');
                        const interactiveArrow = document.getElementById('interative-arrow');
                        // Se não existir o input principal ou seta, não faz nada
                        if (!input || !interactiveArrow) return;
                        // Busca o container pai que contém tanto o input quanto os botões
                        const mainContainer = input.closest('.money_content_right-wrapper');
                        if (!mainContainer) return;
                        // Busca os botões dentro do container principal
                        const increaseBtn = mainContainer.querySelector('[currency-control="increase"]');
                        const decreaseBtn = mainContainer.querySelector('[currency-control="decrease"]');
                        // Se não encontrar os botões, não faz nada
                        if (!increaseBtn || !decreaseBtn) return;
                        // ========== CONTROLE DA SETA ==========
                        let hideTimeout;
                        let isArrowVisible = true;
                        let isButtonInteraction = false;
                        const resetHideTimer = () => {
                          clearTimeout(hideTimeout);
                          if (!isArrowVisible) {
                            showArrow();
                          }
                          hideTimeout = setTimeout(() => {
                            hideArrow();
                          }, 5000);
                        };
                        const hideArrow = () => {
                          isArrowVisible = false;
                          animate(interactiveArrow, {
                            opacity: 0,
                            scale: 0.8
                          }, {
                            duration: 0.4,
                            ease: "circInOut"
                          });
                        };
                        const showArrow = () => {
                          isArrowVisible = true;
                          animate(interactiveArrow, {
                            opacity: 1,
                            scale: 1
                          }, {
                            duration: 0.4,
                            ease: "backOut"
                          });
                        };
                        const rotateArrowDown = () => {
                          animate(interactiveArrow, {
                            rotate: 180,
                            color: '#ef4444'
                          }, {
                            duration: 0.3,
                            ease: "backOut"
                          });
                        };
                        const rotateArrowUp = () => {
                          animate(interactiveArrow, {
                            rotate: 0,
                            color: '#22c55e'
                          }, {
                            duration: 0.3,
                            ease: "backOut"
                          });
                        };
                        // ========== HOVER NO BOTÃO + ==========
                        hover(increaseBtn, (element) => {
                          if (element.classList.contains('disabled')) return;
                          isButtonInteraction = true;
                          animate(element, {
                            scale: 1.08,
                            y: -3,
                            filter: "brightness(1.1)"
                          }, {
                            duration: 0.25,
                            ease: "circOut"
                          });
                          rotateArrowUp();
                          resetHideTimer();
                          const icon = element.querySelector('svg');
                          if (icon) {
                            animate(icon, {
                              scale: 1.15
                            }, {
                              duration: 0.2,
                              ease: "backOut"
                            });
                          }
                          return () => {
                            isButtonInteraction = false;
                            animate(element, {
                              scale: 1,
                              y: 0,
                              filter: "brightness(1)"
                            }, {
                              duration: 0.2,
                              ease: "circInOut"
                            });
                            if (icon) {
                              animate(icon, {
                                scale: 1
                              }, {
                                duration: 0.15
                              });
                            }
                          };
                        });
                        // ========== HOVER NO BOTÃO - ==========
                        hover(decreaseBtn, (element) => {
                          if (element.classList.contains('disabled')) return;
                          isButtonInteraction = true;
                          animate(element, {
                            scale: 1.08,
                            y: -3,
                            filter: "brightness(1.1)"
                          }, {
                            duration: 0.25,
                            ease: "circOut"
                          });
                          rotateArrowDown();
                          resetHideTimer();
                          const icon = element.querySelector('svg');
                          if (icon) {
                            animate(icon, {
                              scale: 1.15
                            }, {
                              duration: 0.2,
                              ease: "backOut"
                            });
                          }
                          return () => {
                            isButtonInteraction = false;
                            animate(element, {
                              scale: 1,
                              y: 0,
                              filter: "brightness(1)"
                            }, {
                              duration: 0.2,
                              ease: "circInOut"
                            });
                            if (icon) {
                              animate(icon, {
                                scale: 1
                              }, {
                                duration: 0.15
                              });
                            }
                          };
                        });
                        // ========== PRESS NO BOTÃO + ==========
                        press(increaseBtn, (element) => {
                          if (element.classList.contains('disabled')) return;
                          isButtonInteraction = true;
                          animate(element, {
                            scale: 0.92,
                            y: 2
                          }, {
                            duration: 0.08,
                            ease: "circIn"
                          });
                          createRippleEffect(element, '#9ca3af');
                          rotateArrowUp();
                          resetHideTimer();
                          return () => {
                            animate(element, {
                              scale: 1.08,
                              y: -3
                            }, {
                              duration: 0.12,
                              ease: "backOut"
                            });
                            setTimeout(() => isButtonInteraction = false, 100);
                          };
                        });
                        // ========== PRESS NO BOTÃO - ==========
                        press(decreaseBtn, (element) => {
                          if (element.classList.contains('disabled')) return;
                          isButtonInteraction = true;
                          animate(element, {
                            scale: 0.92,
                            y: 2
                          }, {
                            duration: 0.08,
                            ease: "circIn"
                          });
                          createRippleEffect(element, '#9ca3af');
                          rotateArrowDown();
                          resetHideTimer();
                          return () => {
                            animate(element, {
                              scale: 1.08,
                              y: -3
                            }, {
                              duration: 0.12,
                              ease: "backOut"
                            });
                            setTimeout(() => isButtonInteraction = false, 100);
                          };
                        });
                        // ========== ESCUTA INPUT PRINCIPAL ==========
                        input.addEventListener('input', () => {
                          if (!isButtonInteraction) {
                            hideArrow();
                            clearTimeout(hideTimeout);
                          }
                        });
                        input.addEventListener('focus', () => {
                          if (!isButtonInteraction) {
                            hideArrow();
                            clearTimeout(hideTimeout);
                          }
                        });
                        // ========== RIPPLE EFFECT ==========
                        function createRippleEffect(element, color) {
                          const ripple = document.createElement('div');
                          ripple.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                width: 10px;
                height: 10px;
                background: ${color};
                border-radius: 50%;
                transform: translate(-50%, -50%);
                pointer-events: none;
                z-index: 1;
                opacity: 0.4;
            `;
                          element.style.position = 'relative';
                          element.style.overflow = 'hidden';
                          element.appendChild(ripple);
                          animate(ripple, {
                            scale: [0, 4],
                            opacity: [0.4, 0]
                          }, {
                            duration: 0.5,
                            ease: "circOut"
                          });
                          setTimeout(() => ripple.remove(), 500);
                        }
                        // ========== GERENCIAMENTO DE CLASSES ==========
                        const updateButtonStates = () => {
                          const current = parseFloat(input.value.replace(/[^\d,]/g, '').replace(',', '.')) || 0;
                          if (current <= 0) {
                            decreaseBtn.classList.add('disabled');
                          } else {
                            decreaseBtn.classList.remove('disabled');
                          }
                        };
                        input.addEventListener('input', updateButtonStates);
                        updateButtonStates();
                        resetHideTimer();
                      }
                    });
                  </script>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="_2-section-calc-ativos">
        <div class="padding-espec">
          <div class="container-espec">
            <div class="ativos_content-wrapper">
              <div class="ativos_left_content-wrapper">
                <div class="heading-and-paragraph">
                  <div class="text-size-medium text-weight-medium">2 / 5</div>
                  <h2 class="heading-style-h2-em width-default">Você sabe onde o seu patrimônio está investido?</h2>
                </div><button data-w-id="28488048-b97f-492d-10e0-f6faa157298f" if-element="button-next" class="button-hero variation money hide-tablet">
                  <div>Próximo</div>
                  <div class="arrow-effect">
                    <div class="arrow is-2">→</div>
                    <div class="arrow">→</div>
                  </div>
                </button>
              </div>
              <div class="ativos_right_content-wrapper">
                <div class="ativos_main-list">
                  <div class="ativos_item dropdown">
                    <div class="icon-dragabble">
                      <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                          <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                          <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                          <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                          <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                        </svg></div>
                    </div>
                    <div>Renda fixa</div>
                    <div class="w-embed"></div>
                    <div class="arrow-dropdown w-embed"><svg width="24" height="16" viewbox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 6L12 10L16 6" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </div>
                  <div class="ativos_item">
                    <div class="icon-dragabble">
                      <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                          <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                          <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                          <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                          <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                        </svg></div>
                    </div>
                    <div>Fundo de investimento</div>
                    <div class="arrow-dropdown w-embed"><svg width="24" height="16" viewbox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 6L12 10L16 6" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </div>
                  <div class="ativos_item">
                    <div class="icon-dragabble">
                      <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                          <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                          <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                          <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                          <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                        </svg></div>
                    </div>
                    <div>Renda variável</div>
                    <div class="arrow-dropdown w-embed"><svg width="24" height="16" viewbox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 6L12 10L16 6" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </div>
                  <div class="ativos_item">
                    <div class="icon-dragabble">
                      <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                          <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                          <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                          <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                          <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                        </svg></div>
                    </div>
                    <div>Poupança</div>
                  </div>
                  <div class="ativos_item">
                    <div class="icon-dragabble">
                      <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                          <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                          <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                          <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                          <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                        </svg></div>
                    </div>
                    <div>Previdência</div>
                  </div>
                  <div class="ativos_item">
                    <div class="icon-dragabble">
                      <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                          <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                          <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                          <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                          <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                        </svg></div>
                    </div>
                    <div>Imóvel</div>
                  </div>
                  <div class="ativos_item">
                    <div class="icon-dragabble">
                      <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                          <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                          <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                          <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                          <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                        </svg></div>
                    </div>
                    <div>COE</div>
                  </div>
                  <div class="ativos_item">
                    <div class="icon-dragabble">
                      <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                          <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                          <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                          <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                          <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                        </svg></div>
                    </div>
                    <div>Operação compromissada</div>
                  </div>
                  <div class="ativos_item">
                    <div class="icon-dragabble">
                      <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                          <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                          <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                          <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                          <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                        </svg></div>
                    </div>
                    <div>Criptoativos</div>
                  </div>
                </div><button fs-list-element="item" fs-list-interactive="true" fs-list-position="10" id="adicionarAtivo" class="ativos_item adicionar">
                  <div class="texto-info ativo">Adicionar outro tipo de ativo</div>
                  <div class="w-embed"><svg width="22" height="22" viewbox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M36.561 19.1582H1.75684" stroke="currentColor" stroke-width="3.48042" stroke-linecap="round" stroke-linejoin="round"></path>
                      <path d="M19.1582 36.56V1.75586" stroke="currentColor" stroke-width="3.48042" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg></div>
                </button>
                <div class="adicionar_nova_ativo"><input placeholder="Adicione outro ativo" id="adicionarNAtivo" class="add_ativo_manual desativado">
                  <a id="adicionar_ativo_novo" href="#" class="ativos_item adicionar_ativo desativado w-button">Adicionar</a>
                </div>
                <div class="drop_ativos_area-wrapper">
                  <div class="drop_header_are-wrapper">
                    <div class="ativos_counter-wrapper">
                      <div class="text-size-xmedium-rem text-weight-medium contador">Seus ativos</div>
                      <div class="counter_ativos">(0)</div>
                    </div>
                    <a href="#" class="ativos_clean-button w-button">Limpar tudo</a>
                  </div>
                  <div class="text-info_wrapper">
                    <div class="text-xmedium-rem">Vamos calcular com base no tipo de Ativo</div>
                    <div class="text-size-medium-rem text-weight-medium text-style-muted-60">Clique ou arraste os ativos acima</div>
                  </div>
                  <div class="ativos_main_drop_area"></div>
                </div>
                <div class="hide">
                  <div id="pillAtivo" class="ativos_item pill">
                    <div class="icon-1x1 w-embed"><svg width="24" height="23" viewbox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 4C0 1.79086 1.79086 0 4 0H20C22.2091 0 24 1.79086 24 4V19C24 21.2091 22.2091 23 20 23H4C1.79086 23 0 21.2091 0 19V4Z" fill="white" fill-opacity="0.68"></path>
                        <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z" fill="#B9B9B9"></path>
                        <path d="M10 15C10 16.1046 9.10457 17 8 17C6.89543 17 6 16.1046 6 15C6 13.8954 6.89543 13 8 13C9.10457 13 10 13.8954 10 15Z" fill="#B9B9B9"></path>
                        <path d="M19 8C19 9.10457 18.1046 10 17 10C15.8954 10 15 9.10457 15 8C15 6.89543 15.8954 6 17 6C18.1046 6 19 6.89543 19 8Z" fill="#B9B9B9"></path>
                        <path d="M19 15C19 16.1046 18.1046 17 17 17C15.8954 17 15 16.1046 15 15C15 13.8954 15.8954 13 17 13C18.1046 13 19 13.8954 19 15Z" fill="#B9B9B9"></path>
                      </svg></div>
                  </div>
                </div>
              </div>
              <div class="next-content hide">
                <div class="number-session">
                  <div class="text-size-medium text-weight-medium session-number">3 / 5</div>
                </div>
                <div class="text-size-medium text-weight-medium">Aloque o seu patrimônio entre os tipos de ativos escolhidos</div>
                <div class="text-size-regular text-weight-medium text-style-muted">Defina quanto você tem investido em cada produto</div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="_3-section-patrimonio-alocation">
        <div class="padding-espec">
          <div class="container-espec">
            <div class="patrimonio_content-wrapper">
              <div class="codigo-itens-produtos w-embed w-script">
                <script>
                    document.addEventListener('DOMContentLoaded', () => {
                      // Aguarda o Motion carregar
                      const waitForMotion = setInterval(() => {
                        if (window.Motion) {
                          clearInterval(waitForMotion);
                          initProductSystem();
                        }
                      }, 50);
                      function initProductSystem() {
                        const {
                          animate,
                          hover,
                          stagger
                        } = window.Motion;
                        // Configuração simplificada
                        const config = {
                          duration: {
                            fast: 0.3,
                            normal: 0.5,
                            slow: 0.6
                          },
                          delay: {
                            deactivate: 1,
                            display: 0.45
                          },
                          animation: {
                            blur: 8,
                            move: 15,
                            rotate: 10
                          },
                          ease: 'circOut'
                        };
                        // Estado global para rastrear interações
                        let globalInteracting = false;
                        let activeSlider = null;
                        // Classe para gerenciar cada item
                        class ProductItem {
                          constructor(element, index) {
                            this.element = element;
                            this.index = index;
                            this.activeDiv = element.querySelector('.active-produto-item');
                            this.disabledDiv = element.querySelector('.disabled-produto-item');
                            this.input = element.querySelector('.currency-input.individual');
                            this.slider = element.querySelector('range-slider');
                            this.sliderThumb = element.querySelector('[data-thumb]');
                            this.pinButton = element.querySelector('.pin-function');
                            // Estado simplificado
                            this.state = {
                              active: false,
                              interacting: false,
                              sliderDragging: false,
                              animating: false,
                              pinned: false // Novo estado para pin
                            };
                            this.deactivateTimer = null;
                            this.init();
                          }
                          init() {
                            if (!this.activeDiv || !this.disabledDiv) return;
                            // Estado inicial
                            this.activeDiv.style.display = 'none';
                            this.disabledDiv.style.display = 'flex';
                            // Oculta o pin inicialmente
                            if (this.pinButton) {
                              this.pinButton.style.display = 'none';
                            }
                            // Setup de eventos
                            this.setupEvents();
                            // Animação de entrada
                            animate(this.element, {
                              opacity: [0, 1],
                              y: [30, 0]
                            }, {
                              duration: config.duration.normal,
                              ease: config.ease,
                              delay: this.index * 0.1
                            });
                          }
                          setupEvents() {
                            // Evento do pin button
                            if (this.pinButton) {
                              this.pinButton.addEventListener('click', (e) => {
                                e.stopPropagation();
                                this.togglePin();
                              });
                              // Previne que o clique no pin afete outros eventos
                              this.pinButton.addEventListener('mousedown', (e) => {
                                e.stopPropagation();
                              });
                            }
                            // Eventos do container
                            const startInteraction = (e) => {
                              // Não inicia se já estiver em outro slider
                              if (activeSlider && activeSlider !== this) return;
                              this.state.interacting = true;
                              this.activate();
                            };
                            const endInteraction = () => {
                              // Só termina se não estiver arrastando slider e não estiver pinado
                              if (!this.state.sliderDragging && !this.state.pinned) {
                                this.state.interacting = false;
                                this.scheduleDeactivate();
                              }
                            };
                            // Mouse no container
                            this.element.addEventListener('mouseenter', startInteraction);
                            this.element.addEventListener('mouseleave', () => {
                              // Só considera saída se não estiver arrastando e não estiver pinado
                              if (!this.state.sliderDragging && !globalInteracting && !this.state.pinned) {
                                endInteraction();
                              }
                            });
                            // Touch no container
                            this.element.addEventListener('touchstart', startInteraction, {
                              passive: true
                            });
                            // Input
                            if (this.input) {
                              this.input.addEventListener('focus', () => {
                                this.state.interacting = true;
                                this.activate();
                              });
                              this.input.addEventListener('blur', () => {
                                if (!this.state.pinned) {
                                  this.state.interacting = false;
                                  this.scheduleDeactivate();
                                }
                              });
                              // Previne desativação ao clicar no input
                              this.input.addEventListener('mousedown', (e) => {
                                e.stopPropagation();
                                this.state.interacting = true;
                              });
                            }
                            // Eventos do Slider
                            if (this.slider) {
                              // Início do arraste
                              const startSliderDrag = (e) => {
                                this.state.sliderDragging = true;
                                this.state.interacting = true;
                                globalInteracting = true;
                                activeSlider = this;
                                this.activate();
                                // Adiciona classe para indicar arraste
                                this.slider.classList.add('dragging');
                              };
                              // Fim do arraste
                              const endSliderDrag = () => {
                                if (this.state.sliderDragging) {
                                  this.state.sliderDragging = false;
                                  globalInteracting = false;
                                  activeSlider = null;
                                  // Remove classe de arraste
                                  this.slider.classList.remove('dragging');
                                  // Verifica se o mouse ainda está sobre o elemento
                                  const mouseOverElement = this.element.matches(':hover');
                                  if (!mouseOverElement && !this.state.pinned) {
                                    this.state.interacting = false;
                                    this.scheduleDeactivate();
                                  }
                                }
                              };
                              // Mouse events no slider e thumb
                              this.slider.addEventListener('mousedown', startSliderDrag);
                              if (this.sliderThumb) {
                                this.sliderThumb.addEventListener('mousedown', startSliderDrag);
                              }
                              // Touch events
                              this.slider.addEventListener('touchstart', startSliderDrag, {
                                passive: true
                              });
                              if (this.sliderThumb) {
                                this.sliderThumb.addEventListener('touchstart', startSliderDrag, {
                                  passive: true
                                });
                              }
                              // Eventos globais para fim do arraste
                              document.addEventListener('mouseup', endSliderDrag);
                              document.addEventListener('touchend', endSliderDrag);
                              // Previne propagação de cliques no slider
                              this.slider.addEventListener('click', (e) => {
                                e.stopPropagation();
                              });
                              // Mantém ativo durante mudanças
                              this.slider.addEventListener('input', () => {
                                this.state.interacting = true;
                                this.activate();
                              });
                            }
                            // Hover effect com Motion
                            hover(this.element, {
                              scale: 1.02,
                              boxShadow: '0 8px 25px rgba(0,0,0,0.1)'
                            });
                          }
                          togglePin() {
                            this.state.pinned = !this.state.pinned;
                            if (this.state.pinned) {
                              // Ativa o pin
                              this.pinButton.classList.add('active');
                              clearTimeout(this.deactivateTimer);
                            } else {
                              // Desativa o pin
                              this.pinButton.classList.remove('active');
                              // Se não estiver interagindo, agenda desativação
                              if (!this.state.interacting && !this.state.sliderDragging) {
                                this.scheduleDeactivate();
                              }
                            }
                            // Animação visual do pin
                            animate(this.pinButton, {
                              scale: [1.2, 1],
                              rotate: this.state.pinned ? 45 : 0
                            }, {
                              duration: 0.3,
                              ease: 'backOut'
                            });
                          }
                          async activate() {
                            if (this.state.active || this.state.animating) return;
                            clearTimeout(this.deactivateTimer);
                            this.state.active = true;
                            this.state.animating = true;
                            // Oculta disabled
                            await animate(this.disabledDiv, {
                              opacity: 0,
                              y: -config.animation.move,
                              filter: `blur(${config.animation.blur}px)`
                            }, {
                              duration: config.duration.fast,
                              ease: 'circIn'
                            }).finished;
                            this.disabledDiv.style.display = 'none';
                            this.activeDiv.style.display = 'block';
                            // Mostra o pin button
                            if (this.pinButton) {
                              this.pinButton.style.display = 'block';
                              animate(this.pinButton, {
                                opacity: [0, 1],
                                scale: [0.8, 1]
                              }, {
                                duration: 0.3,
                                ease: 'backOut',
                                delay: 0.1
                              });
                            }
                            // Mostra active
                            await animate(this.activeDiv, {
                              opacity: [0, 1],
                              y: [config.animation.move, 0],
                              filter: ['blur(5px)', 'blur(0px)']
                            }, {
                              duration: config.duration.normal,
                              ease: 'backOut'
                            }).finished;
                            this.state.animating = false;
                          }
                          scheduleDeactivate() {
                            clearTimeout(this.deactivateTimer);
                            // Não agenda se ainda estiver interagindo, arrastando ou pinado
                            if (this.state.interacting || this.state.sliderDragging || globalInteracting || this.state.pinned) {
                              return;
                            }
                            this.deactivateTimer = setTimeout(() => {
                              if (!this.state.interacting && !this.state.sliderDragging && !globalInteracting && !this.state.pinned) {
                                this.deactivate();
                              }
                            }, config.delay.deactivate * 1000);
                          }
                          async deactivate() {
                            if (!this.state.active || this.state.animating || this.state.sliderDragging || this.state.pinned) return;
                            this.state.active = false;
                            this.state.animating = true;
                            // Oculta o pin button
                            if (this.pinButton) {
                              await animate(this.pinButton, {
                                opacity: 0,
                                scale: 0.8
                              }, {
                                duration: 0.2,
                                ease: 'circIn'
                              }).finished;
                              this.pinButton.style.display = 'none';
                            }
                            // Oculta active
                            await animate(this.activeDiv, {
                              opacity: 0,
                              y: config.animation.move / 2,
                              filter: 'blur(5px)'
                            }, {
                              duration: config.duration.fast,
                              ease: config.ease
                            }).finished;
                            this.activeDiv.style.display = 'none';
                            this.disabledDiv.style.display = 'flex';
                            // Mostra disabled
                            await animate(this.disabledDiv, {
                              opacity: [0, 1],
                              y: [0, 0],
                              filter: ['blur(5px)', 'blur(0px)']
                            }, {
                              duration: config.duration.normal,
                              ease: config.ease
                            }).finished;
                            this.state.animating = false;
                          }
                        }
                        // Inicializa todos os items
                        const items = document.querySelectorAll('.patrimonio_interactive_item');
                        items.forEach((item, index) => new ProductItem(item, index));
                        // Adiciona estilos para feedback visual durante arraste
                        const style = document.createElement('style');
                        style.textContent = `
            range-slider.dragging {
                cursor: grabbing !important;
            }
            range-slider.dragging [data-thumb] {
                cursor: grabbing !important;
                transform: scale(1.1);
                transition: transform 0.2s ease;
            }
        `;
                        document.head.appendChild(style);
                      }
                    });
                  </script>
              </div>
              <div class="patrimonio_top_content-wrapper">
                <div class="heading-and-paragraph">
                  <div class="text-size-medium text-weight-medium">3 / 5</div>
                  <h2 class="heading-style-h2-em width-default v2">Aloque o seu patrimônio</h2>
                </div>
                <div>
                  <div class="next-content v2">
                    <div class="number-session">
                      <div class="text-size-medium text-weight-medium session-number">4 / 5</div>
                    </div>
                    <div class="text-size-regular text-weight-medium text-style-muted">Ver dados</div><button data-w-id="28488048-b97f-492d-10e0-f6faa15729f9" if-element="button-next" class="button-hero variation money v3">
                      <div>Próximo</div>
                      <div class="arrow-effect">
                        <div class="arrow is-2">→</div>
                        <div class="arrow">→</div>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
              <div class="patrimonio_interactive_content-wrapper">
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Renda Fixa</div>
                    <div>CDB,LCI,LCA</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input"><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                      <div class="brl_tag v2">R$</div>
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Renda Fixa</div>
                    <div class="text-block-2">CRI,CRA, DEBENTURE</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Renda Fixa</div>
                    <div>Títulos púbiclos</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Fundo de investimento</div>
                    <div>Ações</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Fundo de investimento</div>
                    <div>Liquidez</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Fundo de investimento</div>
                    <div>Renda Fixa</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Renda variável</div>
                    <div>Ações</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Renda variável</div>
                    <div>Estruturada</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Renda variável</div>
                    <div>Carteira administrada</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Outros</div>
                    <div>Poupança</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Outros</div>
                    <div>Previdência</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Outros</div>
                    <div>Imóvel</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Outros</div>
                    <div>COE</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Outros</div>
                    <div>Operação compromissada</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
                <div class="patrimonio_interactive_item">
                  <div class="ativo_alocated_top-wrapper">
                    <div class="categoria-ativo">Outros</div>
                    <div>Criptoativos</div>
                  </div>
                  <div class="active-produto-item">
                    <div class="patrimonio_value_input">
                      <div class="brl_tag v2">R$</div><input data-currency="true" placeholder="0.000,000,00" input-settings="receive" class="currency-input individual">
                    </div>
                    <div class="range_wrap">
                      <div class="percent-wrapper"><range-slider min="0" max="1" value="0" step="0.00000001" class="slider" id="myRange1"></range-slider>
                        <div>
                          <p class="porcentagem-calculadora">0%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="disabled-produto-item">
                    <div class="valor-produto-disabled">
                      <div class="cifrao">R$</div>
                      <div class="valor-produto">117.000,00</div>
                      <div class="edit-icon">
                        <div class="icon-1x1 w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5.45138 20.0035V14.001" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5.45138 11V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 20.0033V12" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.0002 8.99915V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 20.0036V16.002" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M18.5481 13.0008V3.99707" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2.99609 14.0012H7.90643" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9.54492 8.99923H14.4553" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M16.0928 16.0022H21.0031" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                    <div class="porcentgem-disabled-wrapper">
                      <div class="texto-item">
                        <p class="porcentagem-calculadora-disabled">0%</p>
                        <p class="text-style-muted vv">do seu patrimonio total</p>
                      </div>
                      <div class="background-item-acao"></div>
                    </div>
                  </div><button class="pin-function">
                    <div class="icon-1x1 w-embed"><svg width="20" height="20" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.0002 21.0042V15.502" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.9016 10.499H9.0992C7.71584 10.4993 6.51184 11.445 6.18399 12.789L6.05093 13.3352C5.92349 13.8573 6.04288 14.4089 6.37476 14.8316C6.70664 15.2543 7.21425 15.5011 7.75164 15.5011H16.2552C16.7926 15.5011 17.3002 15.2543 17.6321 14.8316C17.9639 14.4089 18.0833 13.8573 17.9559 13.3352L17.8228 12.789C17.4944 11.4429 16.2872 10.4965 14.9016 10.499V10.499Z" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M14.7509 10.4992L14.1007 6.59759C14.0389 6.22892 14.1878 5.85657 14.4868 5.63219L15.6013 4.79684C15.9458 4.53848 16.0863 4.08866 15.9501 3.68015C15.8139 3.27164 15.4316 2.99609 15.001 2.99609H8.99853C8.56792 2.99609 8.18562 3.27164 8.04945 3.68015C7.91328 4.08866 8.05379 4.53848 8.39828 4.79684L9.51274 5.63219C9.81172 5.85657 9.96065 6.22892 9.8989 6.59759L9.24863 10.4992" stroke="#323232" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg></div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <div class="componente-alocao-float">
        <div class="componente-alocao-left">
          <div class="componente-alocao-top_wrapper">
            <div class="text-size-regular">Patrimônio restante</div>
            <div class="percent-patrimony_wrapper">
              <div>10%</div>
            </div>
          </div>
          <div class="patrimonio_money_wrapper">
            <div class="patrimonio-alocacao_wrapper">
              <div class="cifrao">R$</div>
              <div class="patrimonio-restante">117.000,00</div>
            </div>
            <div class="patrimonio_restante_wrapper">
              <div class="patrimonio-alocacao_wrapper-total">
                <div>Total</div>
                <div class="total-value-inner-wrapper">
                  <div class="cifrao value">R$</div>
                  <div class="patrimonio-total-value">980.000,00</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="componente-alocao-right">
          <div class="componente-alocao-top_wrapper">
            <div class="text-size-regular">Distribuição atual</div><button class="ia-button-alocacao">
              <div class="icon-1x1 w-embed"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewbox="0 0 14 14" fill="none">
                  <g clip-path="url(#clip0_189_424)">
                    <path d="M6.63461 9.19638L6.11568 10.385C5.91641 10.8418 5.28408 10.8418 5.08435 10.385L4.56541 9.19638C4.10341 8.13845 3.27228 7.29658 2.23535 6.83645L0.807347 6.20271C0.35328 6.00111 0.35328 5.34031 0.807347 5.13871L2.19101 4.52458C3.25455 4.05231 4.10061 3.17918 4.55468 2.08578L5.08015 0.819247C5.27521 0.349314 5.92481 0.349314 6.11988 0.819247L6.64535 2.08578C7.09941 3.17965 7.94548 4.05278 9.00901 4.52458L10.3927 5.13871C10.8467 5.34031 10.8467 6.00111 10.3927 6.20271L8.96421 6.83691C7.92775 7.29705 7.09615 8.13891 6.63461 9.19638Z" fill="currentColor"></path>
                    <path d="M11.4247 12.9714L11.2665 13.334C11.1508 13.5995 10.7831 13.5995 10.6673 13.334L10.5091 12.9714C10.2273 12.325 9.71907 11.8103 9.08534 11.5284L8.59814 11.3119C8.33447 11.1948 8.33447 10.8116 8.59814 10.6945L9.05827 10.4901C9.70834 10.2012 10.2254 9.66736 10.5026 8.99909L10.665 8.60709C10.7784 8.33409 11.1559 8.33409 11.2689 8.60709L11.4313 8.99909C11.7085 9.66736 12.2255 10.2012 12.8756 10.4901L13.3357 10.6945C13.5994 10.8116 13.5994 11.1948 13.3357 11.3119L12.8485 11.5284C12.2148 11.8103 11.7066 12.325 11.4247 12.9714Z" fill="currentColor"></path>
                  </g>
                  <defs>
                    <clippath id="clip0_189_424">
                      <rect width="14" height="14" fill="currentColor"></rect>
                    </clippath>
                  </defs>
                </svg></div>
              <div>Preencher com IA</div>
            </button>
          </div>
          <div class="graficos-distribuicao-ativos hide">
            <div class="ativos-graficos-wrapper">
              <div class="ativos-grafico-item">
                <div class="barra-porcentagem-item"></div>
                <div class="nome-porcentagem-item">
                  <div class="porcentagem-inner-item">
                    <div class="porcentagem-float-alocacao">0%</div>
                  </div>
                  <div class="text-inner-item">
                    <div class="nome-produto-float">CDB,LCI,LCA</div>
                  </div>
                </div>
              </div>
              <div class="ativos-grafico-item">
                <div class="barra-porcentagem-item"></div>
                <div class="nome-porcentagem-item">
                  <div class="porcentagem-inner-item">
                    <div class="porcentagem-float-alocacao">0%</div>
                  </div>
                  <div class="text-inner-item">
                    <div class="nome-produto-float">Liquidez</div>
                  </div>
                </div>
              </div>
              <div class="ativos-grafico-item">
                <div class="barra-porcentagem-item"></div>
                <div class="nome-porcentagem-item">
                  <div class="porcentagem-inner-item">
                    <div class="porcentagem-float-alocacao">0%</div>
                  </div>
                  <div class="text-inner-item">
                    <div class="nome-produto-float">Títulos públicos</div>
                  </div>
                </div>
              </div>
              <div class="ativos-grafico-item">
                <div class="barra-porcentagem-item"></div>
                <div class="nome-porcentagem-item">
                  <div class="porcentagem-inner-item">
                    <div class="porcentagem-float-alocacao">0%</div>
                  </div>
                  <div class="text-inner-item">
                    <div class="nome-produto-float">CRI,CRA,DEBENTURE</div>
                  </div>
                </div>
              </div>
              <div class="icon-next w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="24" y="24" width="24" height="24" rx="12" transform="rotate(-180 24 24)" fill="black"></rect>
                  <path d="M10 16L14 12L10 8" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg></div>
            </div>
          </div>
          <div class="ia-input_wrapper">
            <div class="top_ia_wrapper">
              <div class="text-size-regular">Aloque seus ativos</div><button class="close-ia">
                <div class="close-icon w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="24" y="24" width="24" height="24" rx="12" transform="rotate(-180 24 24)" fill="black"></rect>
                    <path d="M15 8L8 15M15 15L8 8" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg></div>
              </button>
            </div>
            <div class="prompt-input-send-wrapper"><input placeholder="Escreva como quer dividir seu patrimônio" class="prompt-input"><button class="process-prompt">
                <div>Alocar</div>
              </button></div>
          </div>
        </div>
      </div>
      <div class="spacer-divider"></div>
    </div>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=688144ef794cddf39f7c4396" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script>
      /**
       * OpenAI Asset Allocation Integration
       * Handles natural language processing for asset allocation requests
       * Integrates with existing patrimony-sync system
       */
      (function() {
        'use strict';
        // Configuration
        const CONFIG = {
          apiEndpoint: 'https://api.openai.com/v1/chat/completions',
          model: 'gpt-4-turbo-preview',
          maxTokens: 1000,
          temperature: 0.3,
          systemPrompt: `You are an asset allocation assistant. Parse natural language requests about investment allocation and return structured JSON data.
Available asset categories:
- Renda Fixa (CDB, LCI, LCA)
- Renda Fixa (CRI, CRA, DEBENTURE)
- Renda Fixa (Títulos públicos)
- Fundo de investimento (Ações)
- Fundo de investimento (Liquidez)
- Fundo de investimento (Renda Fixa)
- Renda variável (Ações)
- Renda variável (FII)
- Renda variável (ETF)
- Renda variável (BDR)
- Previdência privada (PGBL/VGBL)
- Outros investimentos
Instructions:
1. Parse the user's request for allocation changes
2. Return ONLY valid JSON with the requested allocations
3. Ensure total allocation does not exceed 100%
4. Use percentages for allocations
5. Match asset categories exactly as listed above
Response format:
{
  "allocations": [
    {
      "category": "exact category name",
      "subcategory": "exact subcategory name",
      "percentage": number
    }
  ],
  "totalPercentage": number
}`,
          retryAttempts: 3,
          retryDelay: 1000
        };
        // OpenAI Allocation Controller
        class OpenAIAllocationController {
          constructor() {
            this.apiKey = null;
            this.isProcessing = false;
            this.promptInput = null;
            this.processButton = null;
            this.patrimonySystem = null;
            this.loadingState = null;
          }
          async init() {
            // Wait for DOM and patrimony system
            await this.waitForDependencies();
            // Get API key from storage or prompt user
            this.apiKey = await this.getApiKey();
            if (!this.apiKey) {
              console.warn('OpenAI API key not configured');
              return;
            }
            // Setup UI elements
            this.setupUI();
            // Setup event listeners
            this.setupEventListeners();
            console.log('OpenAI Allocation Controller initialized');
          }
          async waitForDependencies() {
            // Wait for DOM
            if (document.readyState !== 'loading') {
              await this.waitForPatrimonySystem();
            } else {
              await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', async () => {
                  await this.waitForPatrimonySystem();
                  resolve();
                });
              });
            }
          }
          async waitForPatrimonySystem() {
            // Wait for patrimony system to be available
            return new Promise((resolve) => {
              const checkInterval = setInterval(() => {
                if (window.PatrimonySync && window.PatrimonySync.initialized) {
                  this.patrimonySystem = window.PatrimonySync;
                  clearInterval(checkInterval);
                  resolve();
                }
              }, 100);
            });
          }
          async getApiKey() {
            // Check localStorage
            let apiKey = localStorage.getItem('openai_api_key');
            if (!apiKey) {
              // Create modal for API key input
              apiKey = await this.promptForApiKey();
              if (apiKey) {
                localStorage.setItem('openai_api_key', apiKey);
              }
            }
            return apiKey;
          }
          async promptForApiKey() {
            return new Promise((resolve) => {
              const modal = this.createApiKeyModal();
              document.body.appendChild(modal);
              const submitButton = modal.querySelector('#submit-api-key');
              const cancelButton = modal.querySelector('#cancel-api-key');
              const input = modal.querySelector('#api-key-input');
              submitButton.addEventListener('click', () => {
                const key = input.value.trim();
                if (key) {
                  document.body.removeChild(modal);
                  resolve(key);
                }
              });
              cancelButton.addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(null);
              });
            });
          }
          createApiKeyModal() {
            const modal = document.createElement('div');
            modal.innerHTML = `
        <div style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
        ">
          <div style="
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
          ">
            <h3 style="margin: 0 0 20px 0;">Configure OpenAI API Key</h3>
            <p style="margin: 0 0 20px 0; color: #666;">
              Para usar a alocação por IA, você precisa configurar sua chave de API OpenAI.
            </p>
            <input
              id="api-key-input"
              type="password"
              placeholder="sk-..."
              style="
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                margin-bottom: 20px;
                box-sizing: border-box;
              "
            />
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
              <button id="cancel-api-key" style="
                padding: 10px 20px;
                border: 1px solid #ddd;
                background: white;
                border-radius: 5px;
                cursor: pointer;
              ">Cancelar</button>
              <button id="submit-api-key" style="
                padding: 10px 20px;
                border: none;
                background: #101010;
                color: white;
                border-radius: 5px;
                cursor: pointer;
              ">Salvar</button>
            </div>
          </div>
        </div>
      `;
            return modal;
          }
          setupUI() {
            // Find UI elements
            this.promptInput = document.querySelector('.prompt-input');
            this.processButton = document.querySelector('.process-prompt');
            if (!this.promptInput || !this.processButton) {
              console.error('Required UI elements not found');
              return;
            }
            // Create loading state
            this.createLoadingState();
          }
          createLoadingState() {
            this.loadingState = document.createElement('div');
            this.loadingState.className = 'ai-loading-state';
            this.loadingState.style.cssText = `
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        z-index: 1000;
      `;
            this.loadingState.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <div class="spinner" style="
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #101010;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          "></div>
          <span>Processando alocação...</span>
        </div>
      `;
            // Add CSS animation
            const style = document.createElement('style');
            style.textContent = `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `;
            document.head.appendChild(style);
            // Add to float component
            const floatComponent = document.querySelector('.componente-alocao-float');
            if (floatComponent) {
              floatComponent.style.position = 'relative';
              floatComponent.appendChild(this.loadingState);
            }
          }
          setupEventListeners() {
            // Process button click
            this.processButton.addEventListener('click', (e) => {
              e.preventDefault();
              this.processAllocationRequest();
            });
            // Enter key on input
            this.promptInput.addEventListener('keypress', (e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                this.processAllocationRequest();
              }
            });
          }
          async processAllocationRequest() {
            if (this.isProcessing) return;
            const prompt = this.promptInput.value.trim();
            if (!prompt) return;
            this.isProcessing = true;
            this.showLoading(true);
            try {
              // Get current allocations for context
              const currentAllocations = this.getCurrentAllocations();
              // Call OpenAI API
              const response = await this.callOpenAI(prompt, currentAllocations);
              // Parse and validate response
              const allocationData = this.parseAIResponse(response);
              if (allocationData) {
                // Apply allocations
                await this.applyAllocations(allocationData);
                // Clear input
                this.promptInput.value = '';
                // Show success feedback
                this.showSuccess();
              } else {
                throw new Error('Invalid response format');
              }
            } catch (error) {
              console.error('Error processing allocation:', error);
              this.showError(error.message);
            } finally {
              this.isProcessing = false;
              this.showLoading(false);
            }
          }
          getCurrentAllocations() {
            if (!this.patrimonySystem) return {};
            const allocations = this.patrimonySystem.getAllocations();
            const mainValue = this.patrimonySystem.getMainValue();
            return {
              totalValue: mainValue,
              allocations: allocations.map(item => ({
                category: item.category,
                subcategory: item.subcategory,
                value: item.value,
                percentage: item.percentage
              }))
            };
          }
          async callOpenAI(prompt, currentAllocations) {
            const contextPrompt = `
Current allocations:
${JSON.stringify(currentAllocations, null, 2)}
User request: ${prompt}
      `;
            const response = await fetch(CONFIG.apiEndpoint, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`
              },
              body: JSON.stringify({
                model: CONFIG.model,
                messages: [{
                    role: 'system',
                    content: CONFIG.systemPrompt
                  },
                  {
                    role: 'user',
                    content: contextPrompt
                  }
                ],
                max_tokens: CONFIG.maxTokens,
                temperature: CONFIG.temperature
              })
            });
            if (!response.ok) {
              const error = await response.json();
              throw new Error(error.error?.message || 'API request failed');
            }
            const data = await response.json();
            return data.choices[0].message.content;
          }
          parseAIResponse(response) {
            try {
              // Extract JSON from response
              const jsonMatch = response.match(/\{[\s\S]*\}/);
              if (!jsonMatch) {
                throw new Error('No JSON found in response');
              }
              const data = JSON.parse(jsonMatch[0]);
              // Validate structure
              if (!data.allocations || !Array.isArray(data.allocations)) {
                throw new Error('Invalid allocation structure');
              }
              // Validate total percentage
              const total = data.allocations.reduce((sum, item) => sum + item.percentage, 0);
              if (total > 100.1) { // Allow small rounding errors
                throw new Error(`Total allocation exceeds 100% (${total.toFixed(1)}%)`);
              }
              return data;
            } catch (error) {
              console.error('Error parsing AI response:', error);
              return null;
            }
          }
          async applyAllocations(allocationData) {
            const mainValue = this.patrimonySystem.getMainValue();
            if (!mainValue || mainValue <= 0) {
              throw new Error('Por favor, defina o valor total do patrimônio primeiro');
            }
            // Map allocations to asset items
            const items = document.querySelectorAll('.patrimonio_interactive_item');
            for (const allocation of allocationData.allocations) {
              const item = this.findAssetItem(items, allocation.category, allocation.subcategory);
              if (item) {
                const value = (mainValue * allocation.percentage) / 100;
                const input = item.querySelector('.currency-input.individual');
                if (input) {
                  // Trigger change event to update through patrimony system
                  input.value = this.formatCurrency(value);
                  input.dispatchEvent(new Event('input', {
                    bubbles: true
                  }));
                }
              }
            }
          }
          findAssetItem(items, category, subcategory) {
            for (const item of items) {
              const categoryEl = item.querySelector('.categoria-ativo');
              const subcategoryEl = item.querySelector('.ativo_alocated_top-wrapper > div:not(.categoria-ativo)');
              if (categoryEl && subcategoryEl) {
                const itemCategory = categoryEl.textContent.trim();
                const itemSubcategory = subcategoryEl.textContent.trim();
                if (itemCategory === category && itemSubcategory === subcategory) {
                  return item;
                }
              }
            }
            return null;
          }
          formatCurrency(value) {
            return value.toLocaleString('pt-BR', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          }
          showLoading(show) {
            if (this.loadingState) {
              this.loadingState.style.display = show ? 'block' : 'none';
            }
          }
          showSuccess() {
            this.showNotification('Alocação aplicada com sucesso!', 'success');
          }
          showError(message) {
            this.showNotification(`Erro: ${message}`, 'error');
          }
          showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'success' ? '#4CAF50' : '#f44336'};
        color: white;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
      `;
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(() => {
              notification.style.animation = 'slideOut 0.3s ease-out';
              setTimeout(() => {
                document.body.removeChild(notification);
              }, 300);
            }, 3000);
          }
        }
        // Initialize when ready
        const controller = new OpenAIAllocationController();
        controller.init().catch(console.error);
        // Export for debugging
        window.OpenAIAllocationController = controller;
      })();
    </script>
  <script src="https://cdn.jsdelivr.net/npm/gsap@3.13.0/dist/gsap.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/currency.js@2.0.4/dist/currency.min.js"></script>
  <script type="module" src="https://cdn.jsdelivr.net/npm/range-slider-element@2/+esm"></script>
  <script type="module">
      import {
        animate,
        hover,
        press
      } from "https://cdn.jsdelivr.net/npm/motion@latest/+esm"
      window.Motion = {
        animate,
        hover,
        press
      }
    </script>
  <script>
      /**
       * Section Visibility Controller
       * Handles conditional visibility for componente-alocao-float based on section 3 visibility
       * Uses Framer Motion for smooth animations
       */
      (function() {
        "use strict";
        // Configuration
        const CONFIG = {
          sectionSelector: "._3-section-patrimonio-alocation",
          floatComponentSelector: ".componente-alocao-float",
          threshold: 0.5, // Trigger when 50% of section is visible (meio da tela)
          rootMargin: "0px 0px -200px 0px", // Aguarda mais antes de triggerar
          animationDuration: 0.6,
          showEasing: "backOut", // Premium spring-like entrance
          hideEasing: "circInOut", // Smooth exit
        };
        // State management
        const SectionVisibility = {
          isInitialized: false,
          section: null,
          floatComponent: null,
          observer: null,
          isVisible: false,
          animationInProgress: false,
          retryCount: 0,
          maxRetries: 20, // Maximum 10 seconds of retrying (20 * 500ms)
        };
        /**
         * Initialize the section visibility system
         */
        function init() {
          if (SectionVisibility.isInitialized) {
            console.warn("Section visibility already initialized");
            return;
          }
          // Wait for DOM and Motion.js to be ready
          if (document.readyState === "loading") {
            document.addEventListener("DOMContentLoaded", initializeWithDelay);
          } else {
            initializeWithDelay();
          }
        }
        /**
         * Initialize with a delay to ensure Webflow has finished loading
         */
        function initializeWithDelay() {
          // Wait for Webflow to potentially finish loading
          setTimeout(() => {
            initialize();
          }, 1000); // Wait 1 second for Webflow to load
        }
        /**
         * Main initialization function
         */
        function initialize() {
          // Check for Motion.js dependency
          if (!window.Motion) {
            console.error("Framer Motion is required for section visibility");
            return;
          }
          // Find required elements
          if (!findElements()) {
            SectionVisibility.retryCount++;
            if (SectionVisibility.retryCount >= SectionVisibility.maxRetries) {
              console.error("Maximum retries reached. Elements not found:", {
                sectionSelector: CONFIG.sectionSelector,
                floatComponentSelector: CONFIG.floatComponentSelector,
                retriesAttempted: SectionVisibility.retryCount,
              });
              return;
            }
            console.warn(
              `Required elements not found, retrying in 500ms (${SectionVisibility.retryCount}/${SectionVisibility.maxRetries})`
            );
            setTimeout(initialize, 500);
            return;
          }
          // Setup intersection observer
          setupIntersectionObserver();
          // Initial state setup
          setupInitialState();
          // Mark as initialized
          SectionVisibility.isInitialized = true;
          console.log("Section visibility system initialized");
          // Dispatch ready event
          document.dispatchEvent(
            new CustomEvent("sectionVisibilityReady", {
              detail: {
                section: SectionVisibility.section,
                floatComponent: SectionVisibility.floatComponent,
              },
            })
          );
        }
        /**
         * Find and cache required DOM elements
         */
        function findElements() {
          // Debug: Log all available elements to help troubleshoot
          console.log("Searching for elements...");
          console.log(
            "Available sections:",
            Array.from(document.querySelectorAll("section")).map((s) => s.className)
          );
          console.log(
            'Available divs with "alocao":',
            Array.from(document.querySelectorAll('div[class*="alocao"]')).map(
              (d) => d.className
            )
          );
          SectionVisibility.section = document.querySelector(CONFIG.sectionSelector);
          SectionVisibility.floatComponent = document.querySelector(
            CONFIG.floatComponentSelector
          );
          if (!SectionVisibility.section) {
            console.warn(`Section with class "${CONFIG.sectionSelector}" not found`);
            return false;
          }
          if (!SectionVisibility.floatComponent) {
            console.warn(
              `Float component with class "${CONFIG.floatComponentSelector}" not found`
            );
            return false;
          }
          console.log("Elements found successfully:", {
            section: SectionVisibility.section,
            floatComponent: SectionVisibility.floatComponent,
          });
          return true;
        }
        /**
         * Setup intersection observer for section visibility detection
         */
        function setupIntersectionObserver() {
          const options = {
            root: null, // Use viewport as root
            rootMargin: CONFIG.rootMargin,
            threshold: CONFIG.threshold,
          };
          SectionVisibility.observer = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
              if (entry.target === SectionVisibility.section) {
                handleSectionVisibilityChange(entry.isIntersecting);
              }
            });
          }, options);
          // Start observing the section
          SectionVisibility.observer.observe(SectionVisibility.section);
        }
        /**
         * Setup initial state for the float component
         */
        function setupInitialState() {
          const {
            animate
          } = window.Motion;
          // Set initial hidden state without animation
          // Start from bottom with more dramatic initial state
          // IMPORTANT: Preserve the CSS centering transform: translate(-50%)
          animate(
            SectionVisibility.floatComponent, {
              opacity: 0,
              scale: 0.8,
              y: 40,
              filter: "blur(8px)",
              x: "-50%", // Preserve horizontal centering
            }, {
              duration: 0
            }
          );
          // Ensure component is initially hidden from screen readers
          SectionVisibility.floatComponent.setAttribute("aria-hidden", "true");
          SectionVisibility.floatComponent.style.pointerEvents = "none";
        }
        /**
         * Handle section visibility changes
         */
        function handleSectionVisibilityChange(isIntersecting) {
          // Prevent duplicate animations
          if (
            SectionVisibility.isVisible === isIntersecting ||
            SectionVisibility.animationInProgress
          ) {
            return;
          }
          SectionVisibility.isVisible = isIntersecting;
          if (isIntersecting) {
            showFloatComponent();
          } else {
            hideFloatComponent();
          }
          // Dispatch visibility change event
          document.dispatchEvent(
            new CustomEvent("sectionVisibilityChanged", {
              detail: {
                isVisible: isIntersecting,
                section: CONFIG.sectionSelector,
                component: CONFIG.floatComponentSelector,
              },
            })
          );
        }
        /**
         * Show the float component with animation
         */
        function showFloatComponent() {
          if (SectionVisibility.animationInProgress) return;
          SectionVisibility.animationInProgress = true;
          const {
            animate
          } = window.Motion;
          // Enable interactions
          SectionVisibility.floatComponent.style.pointerEvents = "auto";
          SectionVisibility.floatComponent.setAttribute("aria-hidden", "false");
          // Premium entrance animation sequence
          // First: Quick micro-scale preparation
          animate(
              SectionVisibility.floatComponent, {
                scale: 0.95,
                x: "-50%", // Maintain centering
              }, {
                duration: 0.1,
                ease: "circOut",
              }
            )
            .then(() => {
              // Main entrance: Spring-like backOut with multiple properties
              return animate(
                SectionVisibility.floatComponent, {
                  opacity: 1,
                  scale: 1,
                  y: 0,
                  filter: "blur(0px)",
                  x: "-50%", // Maintain centering
                }, {
                  duration: CONFIG.animationDuration,
                  ease: CONFIG.showEasing,
                }
              );
            })
            .then(() => {
              // Subtle overshoot and settle for premium feel
              return animate(
                SectionVisibility.floatComponent, {
                  scale: 1.02,
                  filter: "brightness(1.05)",
                  x: "-50%", // Maintain centering
                }, {
                  duration: 0.15,
                  ease: "circOut",
                }
              );
            })
            .then(() => {
              // Final settle
              return animate(
                SectionVisibility.floatComponent, {
                  scale: 1,
                  filter: "brightness(1)",
                  x: "-50%", // Maintain centering
                }, {
                  duration: 0.2,
                  ease: "circInOut",
                }
              );
            })
            .then(() => {
              SectionVisibility.animationInProgress = false;
              // Dispatch show complete event
              document.dispatchEvent(
                new CustomEvent("floatComponentShown", {
                  detail: {
                    component: SectionVisibility.floatComponent
                  },
                })
              );
            });
          console.log("Float component shown with premium animation");
        }
        /**
         * Hide the float component with animation
         */
        function hideFloatComponent() {
          if (SectionVisibility.animationInProgress) return;
          SectionVisibility.animationInProgress = true;
          const {
            animate
          } = window.Motion;
          // Premium exit animation - quick scale down first
          animate(
              SectionVisibility.floatComponent, {
                scale: 0.98,
                filter: "brightness(0.95)",
                x: "-50%", // Maintain centering
              }, {
                duration: 0.1,
                ease: "circIn",
              }
            )
            .then(() => {
              // Main exit animation with smooth circular easing
              return animate(
                SectionVisibility.floatComponent, {
                  opacity: 0,
                  scale: 0.85,
                  y: 30,
                  filter: "blur(4px)",
                  x: "-50%", // Maintain centering
                }, {
                  duration: CONFIG.animationDuration * 0.8, // Slightly faster exit
                  ease: CONFIG.hideEasing,
                }
              );
            })
            .then(() => {
              // Disable interactions after animation
              SectionVisibility.floatComponent.style.pointerEvents = "none";
              SectionVisibility.floatComponent.setAttribute("aria-hidden", "true");
              SectionVisibility.animationInProgress = false;
              // Dispatch hide complete event
              document.dispatchEvent(
                new CustomEvent("floatComponentHidden", {
                  detail: {
                    component: SectionVisibility.floatComponent
                  },
                })
              );
            });
          console.log("Float component hidden with premium animation");
        }
        /**
         * Cleanup function
         */
        function cleanup() {
          if (SectionVisibility.observer) {
            SectionVisibility.observer.disconnect();
            SectionVisibility.observer = null;
          }
          SectionVisibility.isInitialized = false;
          SectionVisibility.section = null;
          SectionVisibility.floatComponent = null;
          SectionVisibility.isVisible = false;
          SectionVisibility.animationInProgress = false;
          console.log("Section visibility system cleaned up");
        }
        /**
         * Public API
         */
        window.SectionVisibility = {
          init,
          cleanup,
          // Getter methods
          isInitialized: () => SectionVisibility.isInitialized,
          isVisible: () => SectionVisibility.isVisible,
          isAnimating: () => SectionVisibility.animationInProgress,
          // Manual control methods
          show: () => {
            if (SectionVisibility.isInitialized && !SectionVisibility.isVisible) {
              handleSectionVisibilityChange(true);
            }
          },
          hide: () => {
            if (SectionVisibility.isInitialized && SectionVisibility.isVisible) {
              handleSectionVisibilityChange(false);
            }
          },
          // Configuration
          getConfig: () => ({
            ...CONFIG
          }),
          updateConfig: (newConfig) => {
            Object.assign(CONFIG, newConfig);
            if (SectionVisibility.isInitialized) {
              console.warn(
                "Configuration updated. Consider reinitializing for full effect."
              );
            }
          },
        };
        // Auto-initialize when script loads
        init();
        // Cleanup on page unload
        window.addEventListener("beforeunload", cleanup);
      })();
    </script>
  <script>
      /**
       * Patrimony Synchronization System
       * Handles synchronized input system for main currency input and section 3 allocations
       * Includes validation to prevent over-allocation
       */
      (function() {
        "use strict";
        // Global state management
        const PatrimonySync = {
          mainValue: 0,
          allocations: new Map(),
          isInitialized: false,
          cacheKey: "patrimony_main_value",
          allocationsCacheKey: "patrimony_allocations",
        };
        // Cache management
        const CacheManager = {
          get(key) {
            try {
              const value = localStorage.getItem(key);
              return value ? JSON.parse(value) : null;
            } catch (e) {
              console.error("Cache get error:", e);
              return null;
            }
          },
          set(key, value) {
            try {
              localStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
              console.error("Cache set error:", e);
            }
          },
          remove(key) {
            try {
              localStorage.removeItem(key);
            } catch (e) {
              console.error("Cache remove error:", e);
            }
          },
        };
        // Utility functions
        const Utils = {
          // Parse currency value from formatted string
          parseCurrencyValue(value) {
            if (!value || typeof value !== "string") return 0;
            const cleanValue = value.replace(/[^\d,]/g, "").replace(",", ".");
            return parseFloat(cleanValue) || 0;
          },
          // Format number to Brazilian currency
          formatCurrency(value) {
            return new Intl.NumberFormat("pt-BR", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(value);
          },
          // Calculate percentage
          calculatePercentage(value, total) {
            if (!total || total === 0) return 0;
            return (value / total) * 100;
          },
          // Format percentage display
          formatPercentage(value) {
            return `${value.toFixed(1)}%`;
          },
          // Debounce function for performance
          debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
              const later = () => {
                clearTimeout(timeout);
                func(...args);
              };
              clearTimeout(timeout);
              timeout = setTimeout(later, wait);
            };
          },
        };
        // Main input synchronization
        const MainInputSync = {
          input: null,
          init() {
            this.input = document.querySelector('[is-main="true"]');
            if (!this.input) {
              console.warn("Main input not found");
              return;
            }
            // Load cached value
            const cachedValue = CacheManager.get(PatrimonySync.cacheKey);
            if (cachedValue !== null && cachedValue > 0) {
              PatrimonySync.mainValue = cachedValue;
              this.input.value = Utils.formatCurrency(cachedValue);
            }
            // Setup event listeners
            this.setupListeners();
          },
          setupListeners() {
            // Listen for currency change events (from existing currency system)
            this.input.addEventListener("currencyChange", (e) => {
              this.handleValueChange(e.detail.value);
            });
            // Listen for direct input changes
            this.input.addEventListener(
              "input",
              Utils.debounce((e) => {
                const value = Utils.parseCurrencyValue(e.target.value);
                this.handleValueChange(value);
              }, 300),
            );
            // Listen for programmatic changes
            this.input.addEventListener("change", (e) => {
              const value = Utils.parseCurrencyValue(e.target.value);
              this.handleValueChange(value);
            });
          },
          handleValueChange(value) {
            PatrimonySync.mainValue = value;
            CacheManager.set(PatrimonySync.cacheKey, value);
            // Dispatch custom event for other components
            document.dispatchEvent(
              new CustomEvent("patrimonyMainValueChanged", {
                detail: {
                  value,
                  formatted: Utils.formatCurrency(value)
                },
              }),
            );
            // Update all section 3 allocations and validate
            AllocationSync.updateAllAllocations();
            AllocationSync.validateAllAllocations();
          },
          getValue() {
            return PatrimonySync.mainValue;
          },
          setValue(value) {
            PatrimonySync.mainValue = value;
            if (this.input) {
              this.input.value = Utils.formatCurrency(value);
              this.input.dispatchEvent(new Event("input", {
                bubbles: true
              }));
            }
          },
        };
        // Section 3 allocation synchronization with validation
        const AllocationSync = {
          items: [],
          init() {
            // Find all patrimonio interactive items
            const containers = document.querySelectorAll(
              ".patrimonio_interactive_item",
            );
            containers.forEach((container, index) => {
              // Find elements within the correct structure
              const activeItem = container.querySelector(".active-produto-item");
              const disabledItem = container.querySelector(".disabled-produto-item");
              if (!activeItem || !disabledItem) return;
              const input = activeItem.querySelector('[input-settings="receive"]');
              const slider = activeItem.querySelector("range-slider");
              const percentageDisplay = activeItem.querySelector(
                ".porcentagem-calculadora",
              );
              // Find elements in disabled state
              const valorProduto = disabledItem.querySelector(".valor-produto");
              const percentageDisabled = disabledItem.querySelector(
                ".porcentagem-calculadora-disabled",
              );
              const backgroundItemAcao = disabledItem.querySelector(
                ".background-item-acao",
              );
              if (input && slider) {
                const item = {
                  container,
                  activeItem,
                  disabledItem,
                  input,
                  slider,
                  percentageDisplay,
                  valorProduto,
                  percentageDisabled,
                  backgroundItemAcao,
                  index,
                  value: 0,
                  percentage: 0,
                  maxAllowed: 0,
                };
                this.items.push(item);
                this.setupItemListeners(item);
              }
            });
            // Load cached allocations
            this.loadCachedAllocations();
          },
          setupItemListeners(item) {
            // Input change listener with validation
            item.input.addEventListener("currencyChange", (e) => {
              this.handleInputChange(item, e.detail.value);
            });
            item.input.addEventListener(
              "input",
              Utils.debounce((e) => {
                const value = Utils.parseCurrencyValue(e.target.value);
                this.handleInputChange(item, value);
              }, 300),
            );
            // Slider change listener with validation
            item.slider.addEventListener("input", (e) => {
              this.handleSliderChange(item, parseFloat(e.target.value));
            });
            // Focus/blur for better UX
            item.input.addEventListener("focus", () => {
              item.container.classList.add("input-focused");
              this.updateMaxAllowed(item);
            });
            item.input.addEventListener("blur", () => {
              item.container.classList.remove("input-focused");
              // Final validation on blur
              this.validateAllocation(item);
            });
          },
          handleInputChange(item, value) {
            const mainValue = MainInputSync.getValue();
            // Calculate max allowed for this item
            const otherAllocations = this.getTotalAllocatedExcept(item);
            const maxAllowed = Math.max(0, mainValue - otherAllocations);
            // Validate and cap the value
            if (value > maxAllowed) {
              value = maxAllowed;
              item.input.value = Utils.formatCurrency(value);
              VisualFeedback.showAllocationWarning(
                item.container,
                `Valor máximo disponível: R$ ${Utils.formatCurrency(maxAllowed)}`,
              );
            }
            item.value = value;
            item.percentage = Utils.calculatePercentage(value, mainValue);
            item.maxAllowed = maxAllowed;
            // Update all displays
            this.updateSlider(item);
            this.updatePercentageDisplay(item);
            this.updateValorProduto(item);
            this.updateBackgroundItemAcao(item);
            // Save to cache
            this.saveAllocations();
            // Dispatch event
            this.dispatchAllocationChange(item);
            // Check total allocation status
            this.checkTotalAllocationStatus();
          },
          handleSliderChange(item, sliderValue) {
            const mainValue = MainInputSync.getValue();
            let value = mainValue * sliderValue;
            // Validate against max allowed
            const otherAllocations = this.getTotalAllocatedExcept(item);
            const maxAllowed = Math.max(0, mainValue - otherAllocations);
            if (value > maxAllowed) {
              value = maxAllowed;
              // Update slider to reflect the capped value
              const cappedSliderValue = mainValue > 0 ? value / mainValue : 0;
              item.slider.value = cappedSliderValue;
              VisualFeedback.showAllocationWarning(
                item.container,
                `Valor máximo disponível: R$ ${Utils.formatCurrency(maxAllowed)}`,
              );
            }
            item.value = value;
            item.percentage =
              value > 0 && mainValue > 0 ? (value / mainValue) * 100 : 0;
            item.maxAllowed = maxAllowed;
            // Update displays
            item.input.value = Utils.formatCurrency(value);
            this.updatePercentageDisplay(item);
            this.updateValorProduto(item);
            this.updateBackgroundItemAcao(item);
            // Save to cache
            this.saveAllocations();
            // Dispatch event
            this.dispatchAllocationChange(item);
            // Check total allocation status
            this.checkTotalAllocationStatus();
          },
          updateSlider(item) {
            const mainValue = MainInputSync.getValue();
            if (mainValue > 0) {
              const sliderValue = item.value / mainValue;
              item.slider.value = Math.min(1, Math.max(0, sliderValue));
            } else {
              item.slider.value = 0;
            }
          },
          updatePercentageDisplay(item) {
            const formattedPercentage = Utils.formatPercentage(item.percentage);
            // Update active percentage display
            if (item.percentageDisplay) {
              item.percentageDisplay.textContent = formattedPercentage;
            }
            // Update disabled percentage display
            if (item.percentageDisabled) {
              item.percentageDisabled.textContent = formattedPercentage;
            }
          },
          updateValorProduto(item) {
            if (item.valorProduto) {
              item.valorProduto.textContent = Utils.formatCurrency(item.value);
            }
          },
          updateBackgroundItemAcao(item) {
            if (item.backgroundItemAcao && window.Motion) {
              const {
                animate
              } = window.Motion;
              const widthPercentage = Math.max(0, Math.min(100, item.percentage));
              animate(
                item.backgroundItemAcao, {
                  width: `${widthPercentage}%`,
                }, {
                  duration: 0.5,
                  easing: "ease-out",
                },
              );
            }
          },
          updateMaxAllowed(item) {
            const mainValue = MainInputSync.getValue();
            const otherAllocations = this.getTotalAllocatedExcept(item);
            item.maxAllowed = Math.max(0, mainValue - otherAllocations);
          },
          validateAllocation(item) {
            const mainValue = MainInputSync.getValue();
            const otherAllocations = this.getTotalAllocatedExcept(item);
            const maxAllowed = Math.max(0, mainValue - otherAllocations);
            if (item.value > maxAllowed) {
              item.value = maxAllowed;
              item.input.value = Utils.formatCurrency(maxAllowed);
              this.handleInputChange(item, maxAllowed);
            }
          },
          validateAllAllocations() {
            const mainValue = MainInputSync.getValue();
            const total = this.getTotalAllocated();
            if (total > mainValue) {
              // Proportionally reduce all allocations
              const ratio = mainValue / total;
              this.items.forEach((item) => {
                const newValue = item.value * ratio;
                this.handleInputChange(item, newValue);
              });
            }
          },
          updateAllAllocations() {
            const mainValue = MainInputSync.getValue();
            this.items.forEach((item) => {
              // Update max allowed for each item
              this.updateMaxAllowed(item);
              // Recalculate percentage based on current value
              if (mainValue > 0) {
                item.percentage = Utils.calculatePercentage(item.value, mainValue);
                this.updateSlider(item);
                this.updatePercentageDisplay(item);
                this.updateValorProduto(item);
                this.updateBackgroundItemAcao(item);
              } else {
                // Reset if main value is 0
                item.value = 0;
                item.percentage = 0;
                item.input.value = Utils.formatCurrency(0);
                item.slider.value = 0;
                this.updatePercentageDisplay(item);
                this.updateValorProduto(item);
                this.updateBackgroundItemAcao(item);
              }
            });
          },
          checkTotalAllocationStatus() {
            const mainValue = MainInputSync.getValue();
            const total = this.getTotalAllocated();
            const remaining = mainValue - total;
            document.dispatchEvent(
              new CustomEvent("allocationStatusChanged", {
                detail: {
                  mainValue,
                  totalAllocated: total,
                  remaining,
                  isFullyAllocated: remaining === 0,
                  isOverAllocated: remaining < 0,
                  percentageAllocated: mainValue > 0 ? (total / mainValue) * 100 : 0,
                },
              }),
            );
          },
          getTotalAllocated() {
            return this.items.reduce((sum, item) => sum + item.value, 0);
          },
          getTotalAllocatedExcept(excludeItem) {
            return this.items.reduce((sum, item) => {
              return item === excludeItem ? sum : sum + item.value;
            }, 0);
          },
          getRemainingValue() {
            const mainValue = MainInputSync.getValue();
            const totalAllocated = this.getTotalAllocated();
            return Math.max(0, mainValue - totalAllocated);
          },
          saveAllocations() {
            const allocations = this.items.map((item) => ({
              index: item.index,
              value: item.value,
              percentage: item.percentage,
            }));
            CacheManager.set(PatrimonySync.allocationsCacheKey, allocations);
          },
          loadCachedAllocations() {
            const cached = CacheManager.get(PatrimonySync.allocationsCacheKey);
            if (!cached || !Array.isArray(cached)) return;
            cached.forEach((cachedItem) => {
              const item = this.items.find((i) => i.index === cachedItem.index);
              if (item) {
                item.value = cachedItem.value;
                item.percentage = cachedItem.percentage;
                item.input.value = Utils.formatCurrency(item.value);
                this.updateSlider(item);
                this.updatePercentageDisplay(item);
                this.updateValorProduto(item);
                this.updateBackgroundItemAcao(item);
              }
            });
            // Validate after loading
            this.validateAllAllocations();
          },
          dispatchAllocationChange(item) {
            document.dispatchEvent(
              new CustomEvent("allocationChanged", {
                detail: {
                  index: item.index,
                  value: item.value,
                  percentage: item.percentage,
                  formatted: Utils.formatCurrency(item.value),
                  remaining: this.getRemainingValue(),
                },
              }),
            );
          },
        };
        // Visual feedback system
        const VisualFeedback = {
          init() {
            // Add CSS for visual feedback
            const style = document.createElement("style");
            style.textContent = `
                .patrimony-sync-active {
                    transition: all 0.3s ease;
                }
                .input-focused {
                    transform: scale(1.02);
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                }
                .porcentagem-calculadora,
                .porcentagem-calculadora-disabled {
                    transition: all 0.3s ease;
                }
                .porcentagem-calculadora.updating,
                .porcentagem-calculadora-disabled.updating {
                    transform: scale(1.1);
                    color: #22c55e;
                }
                .valor-produto {
                    transition: all 0.3s ease;
                }
                .valor-produto.valor-updating {
                    transform: scale(1.05);
                    color: #3b82f6;
                }
                .allocation-warning {
                    color: #ef4444;
                    font-size: 0.875rem;
                    margin-top: 0.5rem;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    position: absolute;
                    background: white;
                    padding: 0.5rem;
                    border-radius: 0.25rem;
                    box-shadow: 0 2px 10px rgba(239, 68, 68, 0.2);
                    z-index: 10;
                }
                .allocation-warning.show {
                    opacity: 1;
                }
                range-slider.dragging {
                    cursor: grabbing;
                }
                range-slider.dragging [data-thumb] {
                    transform: scale(1.2);
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                }
                .allocation-limit-reached {
                    border-color: #ef4444 !important;
                }
                .allocation-progress-bar {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    width: 300px;
                    background: white;
                    border-radius: 8px;
                    padding: 1rem;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                    opacity: 0;
                    transform: translateY(20px);
                    transition: all 0.3s ease;
                    z-index: 1000;
                }
                .allocation-progress-bar.show {
                    opacity: 1;
                    transform: translateY(0);
                }
                .allocation-progress-bar .progress-track {
                    width: 100%;
                    height: 8px;
                    background: #e5e7eb;
                    border-radius: 4px;
                    overflow: hidden;
                    margin: 0.5rem 0;
                }
                .allocation-progress-bar .progress-fill {
                    height: 100%;
                    background: #22c55e;
                    border-radius: 4px;
                    transition: all 0.3s ease;
                }
                .allocation-progress-bar.limit-reached .progress-fill {
                    background: #ef4444;
                }
                .allocation-progress-bar .progress-text {
                    font-size: 0.875rem;
                    color: #6b7280;
                    display: flex;
                    justify-content: space-between;
                }
            `;
            document.head.appendChild(style);
            // Create progress bar element
            this.createProgressBar();
          },
          createProgressBar() {
            const progressBar = document.createElement("div");
            progressBar.className = "allocation-progress-bar";
            progressBar.innerHTML = `
                <div class="progress-text">
                    <span>Patrimônio alocado</span>
                    <span class="progress-percentage">0%</span>
                </div>
                <div class="progress-track">
                    <div class="progress-fill"></div>
                </div>
                <div class="progress-text">
                    <span class="allocated-amount">R$ 0,00</span>
                    <span class="remaining-amount">R$ 0,00 disponível</span>
                </div>
            `;
            document.body.appendChild(progressBar);
            // Listen for allocation changes
            document.addEventListener("allocationStatusChanged", (e) => {
              this.updateProgressBar(e.detail);
            });
          },
          updateProgressBar(status) {
            const progressBar = document.querySelector(".allocation-progress-bar");
            if (!progressBar) return;
            const fill = progressBar.querySelector(".progress-fill");
            const percentage = progressBar.querySelector(".progress-percentage");
            const allocated = progressBar.querySelector(".allocated-amount");
            const remaining = progressBar.querySelector(".remaining-amount");
            // Update values
            const percentAllocated = Math.min(100, status.percentageAllocated);
            fill.style.width = `${percentAllocated}%`;
            percentage.textContent = `${percentAllocated.toFixed(1)}%`;
            allocated.textContent = `R$ ${Utils.formatCurrency(status.totalAllocated)}`;
            remaining.textContent = `R$ ${Utils.formatCurrency(status.remaining)} disponível`;
            // Update styling
            if (status.isFullyAllocated || status.isOverAllocated) {
              progressBar.classList.add("limit-reached");
            } else {
              progressBar.classList.remove("limit-reached");
            }
            // Show progress bar
            progressBar.classList.add("show");
            // Hide after delay if fully allocated
            if (status.isFullyAllocated) {
              setTimeout(() => {
                progressBar.classList.remove("show");
              }, 3000);
            }
          },
          showAllocationWarning(container, message) {
            let warning = container.querySelector(".allocation-warning");
            if (!warning) {
              warning = document.createElement("div");
              warning.className = "allocation-warning";
              container.style.position = "relative";
              container.appendChild(warning);
            }
            warning.textContent = message;
            warning.classList.add("show");
            // Position the warning
            const input = container.querySelector("input");
            if (input) {
              const rect = input.getBoundingClientRect();
              warning.style.top = `${input.offsetTop + rect.height + 5}px`;
              warning.style.left = `${input.offsetLeft}px`;
            }
            // Auto hide
            setTimeout(() => {
              warning.classList.remove("show");
            }, 3000);
            // Add limit reached styling to input
            if (input) {
              input.classList.add("allocation-limit-reached");
              setTimeout(() => {
                input.classList.remove("allocation-limit-reached");
              }, 3000);
            }
          },
        };
        // Public API
        window.PatrimonySync = {
          init() {
            if (document.readyState === "loading") {
              document.addEventListener(
                "DOMContentLoaded",
                this.initialize.bind(this),
              );
            } else {
              this.initialize();
            }
          },
          initialize() {
            // Check for dependencies
            if (!window.currency) {
              console.error("Currency.js is required for PatrimonySync");
              return;
            }
            // Wait for Motion.js to be available
            const waitForMotion = () => {
              if (window.Motion) {
                this.initializeComponents();
              } else {
                setTimeout(waitForMotion, 50);
              }
            };
            waitForMotion();
          },
          initializeComponents() {
            // Initialize components
            VisualFeedback.init();
            MainInputSync.init();
            // Wait a bit for dynamic content to load
            setTimeout(() => {
              AllocationSync.init();
              PatrimonySync.isInitialized = true;
              // Initial status check
              AllocationSync.checkTotalAllocationStatus();
              // Dispatch ready event
              document.dispatchEvent(
                new CustomEvent("patrimonySyncReady", {
                  detail: {
                    mainValue: PatrimonySync.getMainValue(),
                    totalAllocated: PatrimonySync.getTotalAllocated(),
                    remaining: PatrimonySync.getRemainingValue(),
                  },
                }),
              );
            }, 100);
          },
          // Public methods
          getMainValue() {
            return MainInputSync.getValue();
          },
          setMainValue(value) {
            MainInputSync.setValue(value);
          },
          getTotalAllocated() {
            return AllocationSync.getTotalAllocated();
          },
          getRemainingValue() {
            return AllocationSync.getRemainingValue();
          },
          getAllocations() {
            return AllocationSync.items.map((item) => ({
              index: item.index,
              value: item.value,
              percentage: item.percentage,
              formatted: Utils.formatCurrency(item.value),
              maxAllowed: item.maxAllowed,
            }));
          },
          clearCache() {
            CacheManager.remove(PatrimonySync.cacheKey);
            CacheManager.remove(PatrimonySync.allocationsCacheKey);
          },
          reset() {
            this.clearCache();
            MainInputSync.setValue(0);
            AllocationSync.items.forEach((item) => {
              item.value = 0;
              item.percentage = 0;
              item.maxAllowed = 0;
              item.input.value = Utils.formatCurrency(0);
              item.slider.value = 0;
              AllocationSync.updatePercentageDisplay(item);
              AllocationSync.updateValorProduto(item);
              AllocationSync.updateBackgroundItemAcao(item);
            });
            AllocationSync.checkTotalAllocationStatus();
          },
        };
        // Auto-initialize
        window.PatrimonySync.init();
      })();
    </script>
  <script>
      let timeline;
      // Inicializa o gráfico único
      $(".chart_wrap").each(function() {
        timeline = gsap.timeline({
          paused: true
        });
        $(this).find(".chart_column").each(function() {
          let bar = $(this).find(".chart_bar");
          let start = +$(this).attr("start");
          let end = +$(this).attr("end");
          let numberText = $(this).find(".chart_number");
          let number = {
            value: start
          };
          timeline.fromTo(bar, {
              height: start + "%"
            }, {
              height: end + "%"
            }, "<")
            .fromTo(number, {
              value: start
            }, {
              value: end,
              onUpdate: () => numberText.text(Math.round(number.value))
            }, "<");
        });
      });
      // Conecta TODOS os sliders ao mesmo gráfico
      $("range-slider.slider").each(function() {
        // CORRIGE O STEP PROBLEMÁTICO
        this.setAttribute('step', '0.01'); // Muda de 0.00000001 para 0.01
        this.addEventListener('input', () => {
          if (timeline) timeline.progress(this.value);
        });
      });
    </script>
</body>
</html>