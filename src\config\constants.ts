// Application constants
export const APP_CONFIG = {
  version: '1.0.0',
  debug: process.env.NODE_ENV === 'development',
  environment: process.env.NODE_ENV as 'development' | 'production',
} as const;

export const CURRENCY_CONFIG = {
  locale: 'pt-BR',
  currency: 'BRL',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
} as const;

export const ANIMATION_CONFIG = {
  duration: {
    fast: 200,
    normal: 400,
    slow: 800,
  },
  delay: {
    deactivate: 3000,
    display: 100,
  },
  animation: {
    blur: 4,
    move: 10,
    rotate: 2,
  },
  ease: 'power2.out',
} as const;

export const OPENAI_CONFIG = {
  apiEndpoint: 'https://api.openai.com/v1/chat/completions',
  model: 'gpt-3.5-turbo',
  maxTokens: 1000,
  temperature: 0.7,
  retryAttempts: 3,
  retryDelay: 1000,
  systemPrompt: `You are a financial advisor assistant. Parse allocation requests and return JSON with allocations.`,
} as const;

export const CACHE_CONFIG = {
  keyPrefix: 'webflow-patrimony-',
  version: '1.0',
  expirationTime: 24 * 60 * 60 * 1000, // 24 hours
} as const;
