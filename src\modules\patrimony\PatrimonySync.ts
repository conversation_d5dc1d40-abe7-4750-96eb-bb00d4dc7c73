import { EventEmitter } from '../core/EventEmitter';
import {
  AllocationItem,
  PatrimonyState,
  AllocationChangeEvent,
  PatrimonyStatusEvent,
  ValidationResult,
  AllocationValidationConfig,
} from './types';

/**
 * PatrimonySync - Singleton class for managing patrimony state and allocation synchronization
 *
 * This class manages the main patrimony value and all allocations, ensuring consistency
 * and providing real-time synchronization between different components.
 *
 * Events emitted:
 * - 'mainValueChanged': When the main patrimony value changes
 * - 'allocationChanged': When an individual allocation changes
 * - 'statusChanged': When the overall patrimony status changes
 * - 'validationError': When validation fails
 * - 'overflowDetected': When allocations exceed 100%
 */
export class PatrimonySync extends EventEmitter {
  private static instance: PatrimonySync;
  private mainValue: number = 0;
  private allocations: Map<number, AllocationItem> = new Map();
  private validationConfig: AllocationValidationConfig = {
    maxPercentage: 100,
    allowOverflow: false,
    proportionalAdjustment: true,
  };

  private constructor() {
    super();
  }

  /**
   * Get the singleton instance of PatrimonySync
   * @returns PatrimonySync instance
   */
  static getInstance(): PatrimonySync {
    if (!PatrimonySync.instance) {
      PatrimonySync.instance = new PatrimonySync();
    }
    return PatrimonySync.instance;
  }

  /**
   * Get the current main patrimony value
   * @returns Current main value
   */
  getMainValue(): number {
    return this.mainValue;
  }

  /**
   * Set the main patrimony value with validation
   * @param value New main value
   * @throws Error if value is negative
   */
  setMainValue(value: number): void {
    if (value < 0) {
      throw new Error('Main value cannot be negative');
    }

    const previousValue = this.mainValue;
    this.mainValue = value;

    // Recalculate all allocation percentages based on new main value
    this.recalculateAllocationPercentages();

    // Emit events
    this.emit('mainValueChanged', {
      previousValue,
      newValue: value,
      allocations: Array.from(this.allocations.values()),
    });

    this.emitStatusChange();
  }

  /**
   * Update an allocation item
   * @param index Allocation index
   * @param value New allocation value
   */
  updateAllocation(index: number, value: number): void {
    if (value < 0) {
      throw new Error('Allocation value cannot be negative');
    }

    const allocation = this.allocations.get(index);
    if (!allocation) {
      throw new Error(`Allocation with index ${index} not found`);
    }

    const previousValue = allocation.value;
    const percentage = this.mainValue > 0 ? (value / this.mainValue) * 100 : 0;

    // Update allocation
    allocation.value = value;
    allocation.percentage = percentage;

    // Emit allocation change event
    const changeEvent: AllocationChangeEvent = {
      index,
      value,
      percentage,
      formatted: this.formatCurrency(value),
      remaining: this.getRemainingValue(),
    };

    this.emit('allocationChanged', changeEvent);
    this.emitStatusChange();

    // Validate allocations after updating
    const validation = this.validateAllocations();

    if (!validation.isValid && !this.validationConfig.allowOverflow) {
      // Revert if validation fails and overflow is not allowed
      allocation.value = previousValue;
      allocation.percentage = this.mainValue > 0 ? (previousValue / this.mainValue) * 100 : 0;

      this.emit('validationError', {
        index,
        errors: validation.errors,
        attemptedValue: value,
      });

      // Re-emit corrected values
      const correctedEvent: AllocationChangeEvent = {
        index,
        value: previousValue,
        percentage: allocation.percentage,
        formatted: this.formatCurrency(previousValue),
        remaining: this.getRemainingValue(),
      };
      this.emit('allocationChanged', correctedEvent);
      this.emitStatusChange();
      return;
    }

    // Handle overflow if detected
    if (validation.overflowAmount > 0) {
      this.emit('overflowDetected', {
        overflowAmount: validation.overflowAmount,
        totalPercentage: validation.totalPercentage,
        allocations: Array.from(this.allocations.values()),
      });

      // Apply proportional adjustment if enabled
      if (this.validationConfig.proportionalAdjustment) {
        this.applyProportionalAdjustment();
      }
    }
  }

  /**
   * Add a new allocation item
   * @param allocation Allocation item to add
   */
  addAllocation(allocation: AllocationItem): void {
    if (this.allocations.has(allocation.index)) {
      throw new Error(`Allocation with index ${allocation.index} already exists`);
    }

    this.allocations.set(allocation.index, { ...allocation });
    this.emitStatusChange();
  }

  /**
   * Remove an allocation item
   * @param index Allocation index to remove
   */
  removeAllocation(index: number): void {
    if (!this.allocations.has(index)) {
      throw new Error(`Allocation with index ${index} not found`);
    }

    this.allocations.delete(index);
    this.emitStatusChange();
  }

  /**
   * Get an allocation by index
   * @param index Allocation index
   * @returns Allocation item or undefined
   */
  getAllocation(index: number): AllocationItem | undefined {
    return this.allocations.get(index);
  }

  /**
   * Get all allocations
   * @returns Array of all allocation items
   */
  getAllAllocations(): AllocationItem[] {
    return Array.from(this.allocations.values());
  }

  /**
   * Calculate total allocated value
   * @returns Total allocated value
   */
  getTotalAllocated(): number {
    return Array.from(this.allocations.values()).reduce(
      (total, allocation) => total + allocation.value,
      0
    );
  }

  /**
   * Calculate remaining unallocated value
   * @returns Remaining value
   */
  getRemainingValue(): number {
    return Math.max(0, this.mainValue - this.getTotalAllocated());
  }

  /**
   * Get current patrimony state
   * @returns Current state object
   */
  getState(): PatrimonyState {
    const totalAllocated = this.getTotalAllocated();
    const remaining = this.getRemainingValue();
    const percentageAllocated = this.mainValue > 0 ? (totalAllocated / this.mainValue) * 100 : 0;

    return {
      mainValue: this.mainValue,
      totalAllocated,
      remaining,
      isFullyAllocated: remaining === 0 && totalAllocated > 0,
      isOverAllocated: totalAllocated > this.mainValue,
      percentageAllocated,
    };
  }

  /**
   * Validate all allocations
   * @returns Validation result
   */
  validateAllocations(): ValidationResult {
    const totalAllocated = this.getTotalAllocated();
    const totalPercentage = this.mainValue > 0 ? (totalAllocated / this.mainValue) * 100 : 0;
    const overflowAmount = Math.max(0, totalAllocated - this.mainValue);

    const errors: string[] = [];
    const warnings: string[] = [];

    // Check for overflow
    if (totalPercentage > this.validationConfig.maxPercentage) {
      errors.push(
        `Total allocation (${totalPercentage.toFixed(2)}%) exceeds maximum allowed (${this.validationConfig.maxPercentage}%)`
      );
    }

    // Check individual allocations
    this.allocations.forEach((allocation, index) => {
      if (allocation.percentage > allocation.maxAllowed) {
        warnings.push(
          `Allocation ${index} (${allocation.percentage.toFixed(2)}%) exceeds recommended maximum (${allocation.maxAllowed}%)`
        );
      }
    });

    // Check for negative values
    if (this.mainValue < 0) {
      errors.push('Main value cannot be negative');
    }

    this.allocations.forEach((allocation, index) => {
      if (allocation.value < 0) {
        errors.push(`Allocation ${index} cannot have negative value`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      totalPercentage,
      overflowAmount,
    };
  }

  /**
   * Apply proportional adjustment to reduce overflow
   */
  private applyProportionalAdjustment(): void {
    const totalAllocated = this.getTotalAllocated();

    if (totalAllocated <= this.mainValue) {
      return; // No adjustment needed
    }

    const adjustmentFactor = this.mainValue / totalAllocated;

    // Apply proportional reduction to all allocations
    this.allocations.forEach((allocation, index) => {
      const adjustedValue = allocation.value * adjustmentFactor;
      allocation.value = adjustedValue;
      allocation.percentage = this.mainValue > 0 ? (adjustedValue / this.mainValue) * 100 : 0;

      // Emit individual allocation change
      const changeEvent: AllocationChangeEvent = {
        index,
        value: adjustedValue,
        percentage: allocation.percentage,
        formatted: this.formatCurrency(adjustedValue),
        remaining: this.getRemainingValue(),
      };

      this.emit('allocationChanged', changeEvent);
    });

    this.emit('proportionalAdjustmentApplied', {
      adjustmentFactor,
      allocations: Array.from(this.allocations.values()),
    });

    this.emitStatusChange();
  }

  /**
   * Recalculate all allocation percentages based on current main value
   */
  private recalculateAllocationPercentages(): void {
    this.allocations.forEach((allocation) => {
      allocation.percentage = this.mainValue > 0 ? (allocation.value / this.mainValue) * 100 : 0;
    });
  }

  /**
   * Emit status change event
   */
  private emitStatusChange(): void {
    const statusEvent: PatrimonyStatusEvent = this.getState();
    this.emit('statusChanged', statusEvent);
  }

  /**
   * Format currency value (basic implementation)
   * @param value Value to format
   * @returns Formatted currency string
   */
  private formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  }

  /**
   * Clear all allocations
   */
  clearAllocations(): void {
    this.allocations.clear();
    this.emitStatusChange();
  }

  /**
   * Reset patrimony to initial state
   */
  reset(): void {
    this.mainValue = 0;
    this.allocations.clear();
    this.emitStatusChange();
  }

  /**
   * Update validation configuration
   * @param config New validation configuration
   */
  updateValidationConfig(config: Partial<AllocationValidationConfig>): void {
    this.validationConfig = { ...this.validationConfig, ...config };
  }

  /**
   * Get current validation configuration
   * @returns Current validation configuration
   */
  getValidationConfig(): AllocationValidationConfig {
    return { ...this.validationConfig };
  }
}
