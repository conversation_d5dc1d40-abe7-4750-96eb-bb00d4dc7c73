import { Utils } from '$modules/core/Utils';

describe('Utils', () => {
  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = Utils.generateId();
      const id2 = Utils.generateId();

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^id-\d+-[a-z0-9]+$/);
    });

    it('should use custom prefix', () => {
      const id = Utils.generateId('custom');
      expect(id).toMatch(/^custom-\d+-[a-z0-9]+$/);
    });
  });

  describe('deepClone', () => {
    it('should clone primitive values', () => {
      expect(Utils.deepClone(42)).toBe(42);
      expect(Utils.deepClone('test')).toBe('test');
      expect(Utils.deepClone(true)).toBe(true);
      expect(Utils.deepClone(null)).toBe(null);
    });

    it('should clone arrays', () => {
      const original = [1, 2, { nested: true }];
      const cloned = Utils.deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned[2]).not.toBe(original[2]);
    });

    it('should clone objects', () => {
      const original = { a: 1, b: { nested: true } };
      const cloned = Utils.deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.b).not.toBe(original.b);
    });

    it('should clone dates', () => {
      const original = new Date('2023-01-01');
      const cloned = Utils.deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
    });
  });

  describe('isEmpty', () => {
    it('should return true for empty values', () => {
      expect(Utils.isEmpty(null)).toBe(true);
      expect(Utils.isEmpty(undefined)).toBe(true);
      expect(Utils.isEmpty('')).toBe(true);
      expect(Utils.isEmpty('   ')).toBe(true);
      expect(Utils.isEmpty([])).toBe(true);
      expect(Utils.isEmpty({})).toBe(true);
    });

    it('should return false for non-empty values', () => {
      expect(Utils.isEmpty('test')).toBe(false);
      expect(Utils.isEmpty([1])).toBe(false);
      expect(Utils.isEmpty({ a: 1 })).toBe(false);
      expect(Utils.isEmpty(0)).toBe(false);
      expect(Utils.isEmpty(false)).toBe(false);
    });
  });

  describe('clamp', () => {
    it('should clamp values within range', () => {
      expect(Utils.clamp(5, 0, 10)).toBe(5);
      expect(Utils.clamp(-5, 0, 10)).toBe(0);
      expect(Utils.clamp(15, 0, 10)).toBe(10);
    });
  });

  describe('lerp', () => {
    it('should interpolate between values', () => {
      expect(Utils.lerp(0, 10, 0.5)).toBe(5);
      expect(Utils.lerp(0, 10, 0)).toBe(0);
      expect(Utils.lerp(0, 10, 1)).toBe(10);
    });

    it('should clamp factor to 0-1 range', () => {
      expect(Utils.lerp(0, 10, -0.5)).toBe(0);
      expect(Utils.lerp(0, 10, 1.5)).toBe(10);
    });
  });

  describe('mapRange', () => {
    it('should map values between ranges', () => {
      expect(Utils.mapRange(5, 0, 10, 0, 100)).toBe(50);
      expect(Utils.mapRange(0, 0, 10, 0, 100)).toBe(0);
      expect(Utils.mapRange(10, 0, 10, 0, 100)).toBe(100);
    });
  });

  describe('roundTo', () => {
    it('should round to specified decimal places', () => {
      expect(Utils.roundTo(3.14159, 2)).toBe(3.14);
      expect(Utils.roundTo(3.14159, 0)).toBe(3);
      expect(Utils.roundTo(3.14159, 4)).toBe(3.1416);
    });
  });

  describe('parseNumber', () => {
    it('should parse Brazilian format numbers', () => {
      expect(Utils.parseNumber('1.234,56', 'pt-BR')).toBe(1234.56);
      expect(Utils.parseNumber('R$ 1.234,56', 'pt-BR')).toBe(1234.56);
      expect(Utils.parseNumber('1234,56', 'pt-BR')).toBe(1234.56);
    });

    it('should parse US format numbers', () => {
      expect(Utils.parseNumber('1,234.56', 'en-US')).toBe(1234.56);
      expect(Utils.parseNumber('$1,234.56', 'en-US')).toBe(1234.56);
    });

    it('should return 0 for invalid numbers', () => {
      expect(Utils.parseNumber('invalid', 'pt-BR')).toBe(0);
      expect(Utils.parseNumber('', 'pt-BR')).toBe(0);
    });
  });

  describe('isValidEmail', () => {
    it('should validate email addresses', () => {
      expect(Utils.isValidEmail('<EMAIL>')).toBe(true);
      expect(Utils.isValidEmail('<EMAIL>')).toBe(true);
      expect(Utils.isValidEmail('invalid-email')).toBe(false);
      expect(Utils.isValidEmail('test@')).toBe(false);
      expect(Utils.isValidEmail('@example.com')).toBe(false);
    });
  });

  describe('capitalize', () => {
    it('should capitalize first letter', () => {
      expect(Utils.capitalize('hello')).toBe('Hello');
      expect(Utils.capitalize('HELLO')).toBe('Hello');
      expect(Utils.capitalize('hELLO')).toBe('Hello');
    });
  });

  describe('toKebabCase', () => {
    it('should convert to kebab-case', () => {
      expect(Utils.toKebabCase('camelCase')).toBe('camel-case');
      expect(Utils.toKebabCase('PascalCase')).toBe('pascal-case');
      expect(Utils.toKebabCase('snake_case')).toBe('snake-case');
      expect(Utils.toKebabCase('space separated')).toBe('space-separated');
    });
  });

  describe('toCamelCase', () => {
    it('should convert to camelCase', () => {
      expect(Utils.toCamelCase('kebab-case')).toBe('kebabCase');
      expect(Utils.toCamelCase('snake_case')).toBe('snakeCase');
      expect(Utils.toCamelCase('space separated')).toBe('spaceSeparated');
      expect(Utils.toCamelCase('PascalCase')).toBe('pascalCase');
    });
  });

  describe('wait', () => {
    it('should wait for specified time', async () => {
      const startTime = Date.now();
      await Utils.wait(100);
      const endTime = Date.now();

      expect(endTime - startTime).toBeGreaterThanOrEqual(90);
    });
  });

  describe('retry', () => {
    it('should retry function on failure', async () => {
      let attempts = 0;
      const fn = jest.fn().mockImplementation(() => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Test error');
        }
        return 'success';
      });

      const result = await Utils.retry(fn, 3, 10);

      expect(result).toBe('success');
      expect(fn).toHaveBeenCalledTimes(3);
    });

    it('should throw last error after max attempts', async () => {
      const fn = jest.fn().mockRejectedValue(new Error('Test error'));

      await expect(Utils.retry(fn, 2, 10)).rejects.toThrow('Test error');
      expect(fn).toHaveBeenCalledTimes(2);
    });
  });

  describe('groupBy', () => {
    it('should group array items by key', () => {
      const items = [
        { category: 'A', value: 1 },
        { category: 'B', value: 2 },
        { category: 'A', value: 3 },
      ];

      const grouped = Utils.groupBy(items, (item) => item.category);

      expect(grouped.A).toHaveLength(2);
      expect(grouped.B).toHaveLength(1);
      expect(grouped.A![0]!.value).toBe(1);
      expect(grouped.A![1]!.value).toBe(3);
    });
  });

  describe('unique', () => {
    it('should remove duplicates from array', () => {
      const items = [1, 2, 2, 3, 3, 3];
      const unique = Utils.unique(items);

      expect(unique).toEqual([1, 2, 3]);
    });

    it('should remove duplicates using key function', () => {
      const items = [
        { id: 1, name: 'A' },
        { id: 2, name: 'B' },
        { id: 1, name: 'C' },
      ];

      const unique = Utils.unique(items, (item) => item.id);

      expect(unique).toHaveLength(2);
      expect(unique[0]!.name).toBe('A');
      expect(unique[1]!.name).toBe('B');
    });
  });
});
