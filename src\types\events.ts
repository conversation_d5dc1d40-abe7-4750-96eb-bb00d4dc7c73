// Event types for the application
import type { CurrencyChangeEvent } from '../modules/currency/types';
import type { AllocationChangeEvent, PatrimonyStatusEvent } from '../modules/patrimony/types';
import type { ProductActivationEvent } from '../modules/products/types';
import type { SectionVisibilityEvent } from '../modules/visibility/types';

export interface AppEventMap {
  'currency:change': CurrencyChangeEvent;
  'patrimony:allocation-change': AllocationChangeEvent;
  'patrimony:status-change': PatrimonyStatusEvent;
  'product:activation': ProductActivationEvent;
  'visibility:section-change': SectionVisibilityEvent;
  'app:error': Error;
  'app:ready': void;
}
