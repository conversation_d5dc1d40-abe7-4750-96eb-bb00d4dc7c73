import { EventEmitter } from '$modules/core/EventEmitter';

describe('EventEmitter', () => {
  let emitter: EventEmitter;

  beforeEach(() => {
    emitter = new EventEmitter();
  });

  describe('on', () => {
    it('should add event listener', () => {
      const listener = jest.fn();
      emitter.on('test', listener);

      expect(emitter.listenerCount('test')).toBe(1);
    });

    it('should allow multiple listeners for same event', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();

      emitter.on('test', listener1);
      emitter.on('test', listener2);

      expect(emitter.listenerCount('test')).toBe(2);
    });
  });

  describe('emit', () => {
    it('should call all listeners with correct data', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();
      const testData = { message: 'test' };

      emitter.on('test', listener1);
      emitter.on('test', listener2);
      emitter.emit('test', testData);

      expect(listener1).toHaveBeenCalledWith(testData);
      expect(listener2).toHaveBeenCalledWith(testData);
    });

    it('should handle errors in listeners gracefully', () => {
      const errorListener = jest.fn(() => {
        throw new Error('Test error');
      });
      const normalListener = jest.fn();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      emitter.on('test', errorListener);
      emitter.on('test', normalListener);
      emitter.emit('test', 'data');

      expect(errorListener).toHaveBeenCalled();
      expect(normalListener).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('off', () => {
    it('should remove specific listener', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();

      emitter.on('test', listener1);
      emitter.on('test', listener2);
      emitter.off('test', listener1);

      expect(emitter.listenerCount('test')).toBe(1);

      emitter.emit('test', 'data');
      expect(listener1).not.toHaveBeenCalled();
      expect(listener2).toHaveBeenCalled();
    });

    it('should remove all listeners when no specific listener provided', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();

      emitter.on('test', listener1);
      emitter.on('test', listener2);
      emitter.off('test');

      expect(emitter.listenerCount('test')).toBe(0);
    });
  });

  describe('once', () => {
    it('should call listener only once', () => {
      const listener = jest.fn();

      emitter.once('test', listener);
      emitter.emit('test', 'data1');
      emitter.emit('test', 'data2');

      expect(listener).toHaveBeenCalledTimes(1);
      expect(listener).toHaveBeenCalledWith('data1');
    });
  });

  describe('removeAllListeners', () => {
    it('should remove all listeners for all events', () => {
      emitter.on('event1', jest.fn());
      emitter.on('event2', jest.fn());

      expect(emitter.eventNames()).toHaveLength(2);

      emitter.removeAllListeners();

      expect(emitter.eventNames()).toHaveLength(0);
    });
  });

  describe('eventNames', () => {
    it('should return array of event names with listeners', () => {
      emitter.on('event1', jest.fn());
      emitter.on('event2', jest.fn());

      const eventNames = emitter.eventNames();
      expect(eventNames).toContain('event1');
      expect(eventNames).toContain('event2');
      expect(eventNames).toHaveLength(2);
    });
  });
});
