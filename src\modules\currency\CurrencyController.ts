import { EventEmitter } from '../core/EventEmitter';
import { C<PERSON><PERSON>cyFormatter } from './CurrencyFormatter';
import type { CurrencyInputConfig, CurrencyChangeEvent, CurrencyValue } from './types';

/**
 * CurrencyController manages currency input fields with real-time formatting
 * and validation. Handles user interactions and dispatches currency change events.
 */
export class CurrencyController extends EventEmitter {
  private formatter: CurrencyFormatter;
  private inputs: Map<HTMLInputElement, CurrencyInputConfig> = new Map();
  private debounceTimers: Map<HTMLInputElement, NodeJS.Timeout> = new Map();
  private readonly DEBOUNCE_DELAY = 300;

  constructor() {
    super();
    this.formatter = CurrencyFormatter.getInstance();
  }

  /**
   * Initialize a currency input field with formatting and event handling
   * @param input HTML input element
   * @param config Optional configuration for the input
   */
  initializeInput(input: HTMLInputElement, config: CurrencyInputConfig = {}): void {
    if (!input || input.type !== 'text') {
      console.warn('CurrencyController: Input must be a text input element');
      return;
    }

    // Store configuration
    this.inputs.set(input, config);

    // Set initial value if provided
    const initialValue = input.value;
    if (initialValue) {
      const parsedValue = this.formatter.parseValue(initialValue);
      this.updateInputValue(input, parsedValue);
    }

    // Setup event listeners
    this.setupInputEvents(input);

    // Setup control buttons if they exist
    this.setupControlButtons(input);

    // Mark input as initialized
    input.dataset.currencyInitialized = 'true';
  }

  /**
   * Setup event listeners for an input field
   * @param input HTML input element
   */
  private setupInputEvents(input: HTMLInputElement): void {
    // Handle input events with debouncing
    input.addEventListener('input', (event) => {
      this.handleInputEvent(input, (event.target as HTMLInputElement).value);
    });

    // Handle focus events
    input.addEventListener('focus', () => {
      this.handleFocusEvent(input);
    });

    // Handle blur events
    input.addEventListener('blur', () => {
      this.handleBlurEvent(input);
    });

    // Handle paste events
    input.addEventListener('paste', (event) => {
      setTimeout(() => {
        this.handleInputEvent(input, input.value);
      }, 0);
    });

    // Prevent invalid characters
    input.addEventListener('keypress', (event) => {
      this.handleKeyPress(event);
    });
  }

  /**
   * Handle input events with debouncing and formatting
   * @param input HTML input element
   * @param value Current input value
   */
  private handleInputEvent(input: HTMLInputElement, value: string): void {
    // Clear existing debounce timer
    const existingTimer = this.debounceTimers.get(input);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new debounce timer
    const timer = setTimeout(() => {
      this.processInputValue(input, value);
      this.debounceTimers.delete(input);
    }, this.DEBOUNCE_DELAY);

    this.debounceTimers.set(input, timer);
  }

  /**
   * Process and format input value
   * @param input HTML input element
   * @param value Raw input value
   */
  private processInputValue(input: HTMLInputElement, value: string): void {
    const config = this.inputs.get(input);
    if (!config) return;

    // Parse the value
    let numericValue = this.formatter.parseValue(value);

    // Apply validation
    numericValue = this.validateValue(numericValue, config);

    // Update input with formatted value
    this.updateInputValue(input, numericValue);

    // Emit change event
    this.emitChangeEvent(input, numericValue);
  }

  /**
   * Validate numeric value against configuration constraints
   * @param value Numeric value
   * @param config Input configuration
   * @returns Validated value
   */
  private validateValue(value: number, config: CurrencyInputConfig): number {
    // Check if negative values are allowed
    if (!config.allowNegative && value < 0) {
      value = 0;
    }

    // Apply min/max constraints
    if (config.minValue !== undefined && value < config.minValue) {
      value = config.minValue;
    }

    if (config.maxValue !== undefined && value > config.maxValue) {
      value = config.maxValue;
    }

    return value;
  }

  /**
   * Update input value with formatted currency
   * @param input HTML input element
   * @param value Numeric value
   */
  private updateInputValue(input: HTMLInputElement, value: number): void {
    const formatted = this.formatter.formatValue(value);
    if (input.value !== formatted) {
      const cursorPosition = input.selectionStart || 0;
      input.value = formatted;

      // Restore cursor position (approximately)
      const newPosition = Math.min(cursorPosition, formatted.length);
      input.setSelectionRange(newPosition, newPosition);
    }
  }

  /**
   * Handle focus events for better UX
   * @param input HTML input element
   */
  private handleFocusEvent(input: HTMLInputElement): void {
    // Select all text on focus for easy editing
    setTimeout(() => {
      input.select();
    }, 0);

    // Add focused class for styling
    input.classList.add('currency-focused');
  }

  /**
   * Handle blur events
   * @param input HTML input element
   */
  private handleBlurEvent(input: HTMLInputElement): void {
    // Remove focused class
    input.classList.remove('currency-focused');

    // Ensure value is properly formatted
    const currentValue = this.formatter.parseValue(input.value);
    this.updateInputValue(input, currentValue);
  }

  /**
   * Handle keypress events to prevent invalid characters
   * @param event Keyboard event
   */
  private handleKeyPress(event: KeyboardEvent): void {
    const char = event.key;
    const input = event.target as HTMLInputElement;
    const config = this.inputs.get(input);

    // Allow control keys
    if (event.ctrlKey || event.metaKey || char.length > 1) {
      return;
    }

    // Allow numbers
    if (/\d/.test(char)) {
      return;
    }

    // Allow decimal separator (comma for Brazilian format)
    if (char === ',' && !input.value.includes(',')) {
      return;
    }

    // Allow negative sign if configured
    if (char === '-' && config?.allowNegative && input.selectionStart === 0) {
      return;
    }

    // Prevent all other characters
    event.preventDefault();
  }

  /**
   * Setup increment/decrement control buttons
   * @param input HTML input element
   */
  setupControlButtons(input: HTMLInputElement): void {
    const container = input.parentElement;
    if (!container) return;

    // Look for increment/decrement buttons
    const incrementBtn = container.querySelector('[data-currency-increment]') as HTMLButtonElement;
    const decrementBtn = container.querySelector('[data-currency-decrement]') as HTMLButtonElement;

    if (incrementBtn) {
      incrementBtn.addEventListener('click', (event) => {
        event.preventDefault();
        this.incrementValue(input);
      });
    }

    if (decrementBtn) {
      decrementBtn.addEventListener('click', (event) => {
        event.preventDefault();
        this.decrementValue(input);
      });
    }
  }

  /**
   * Increment input value with smart increment logic
   * @param input HTML input element
   */
  private incrementValue(input: HTMLInputElement): void {
    const currentValue = this.formatter.parseValue(input.value);
    const increment = this.getSmartIncrement(currentValue);
    const newValue = currentValue + increment;

    const config = this.inputs.get(input);
    const validatedValue = this.validateValue(newValue, config || {});

    this.updateInputValue(input, validatedValue);
    this.emitChangeEvent(input, validatedValue);
  }

  /**
   * Decrement input value with smart decrement logic
   * @param input HTML input element
   */
  private decrementValue(input: HTMLInputElement): void {
    const currentValue = this.formatter.parseValue(input.value);
    const increment = this.getSmartIncrement(currentValue);
    const newValue = currentValue - increment;

    const config = this.inputs.get(input);
    const validatedValue = this.validateValue(newValue, config || {});

    this.updateInputValue(input, validatedValue);
    this.emitChangeEvent(input, validatedValue);
  }

  /**
   * Get smart increment value based on current value
   * @param value Current numeric value
   * @returns Increment amount
   */
  private getSmartIncrement(value: number): number {
    if (value < 10) return 1;
    if (value < 100) return 10;
    if (value < 1000) return 50;
    if (value < 10000) return 100;
    if (value < 100000) return 500;
    return 1000;
  }

  /**
   * Emit currency change event
   * @param input HTML input element
   * @param value Numeric value
   */
  private emitChangeEvent(input: HTMLInputElement, value: number): void {
    const config = this.inputs.get(input);
    const currencyValue: CurrencyValue = this.formatter.createCurrencyObject(value);

    const changeEvent: CurrencyChangeEvent = {
      value,
      formatted: currencyValue.formatted,
      input,
    };

    // Call config callback if provided
    if (config?.onValueChange) {
      config.onValueChange(currencyValue);
    }

    // Emit global event
    this.emit('currencyChange', changeEvent);

    // Emit specific event for main inputs
    if (config?.isMain) {
      this.emit('mainCurrencyChange', changeEvent);
    }
  }

  /**
   * Get current value of an input
   * @param input HTML input element
   * @returns Current numeric value
   */
  getValue(input: HTMLInputElement): number {
    return this.formatter.parseValue(input.value);
  }

  /**
   * Set value of an input programmatically
   * @param input HTML input element
   * @param value Numeric value to set
   */
  setValue(input: HTMLInputElement, value: number): void {
    const config = this.inputs.get(input);
    const validatedValue = this.validateValue(value, config || {});
    this.updateInputValue(input, validatedValue);
    this.emitChangeEvent(input, validatedValue);
  }

  /**
   * Remove currency formatting from an input
   * @param input HTML input element
   */
  destroy(input: HTMLInputElement): void {
    // Clear debounce timer
    const timer = this.debounceTimers.get(input);
    if (timer) {
      clearTimeout(timer);
      this.debounceTimers.delete(input);
    }

    // Remove from maps
    this.inputs.delete(input);

    // Remove dataset marker
    delete input.dataset.currencyInitialized;

    // Remove event listeners would require storing references
    // For now, we'll rely on garbage collection when input is removed from DOM
  }

  /**
   * Get all initialized inputs
   * @returns Array of initialized input elements
   */
  getInitializedInputs(): HTMLInputElement[] {
    return Array.from(this.inputs.keys());
  }
}
