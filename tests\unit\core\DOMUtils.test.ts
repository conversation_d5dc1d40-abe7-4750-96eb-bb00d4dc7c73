import { DOMUtils } from '$modules/core/DOMUtils';
import { createMockElement, triggerEvent, nextTick } from '../../utils/test-utils';

describe('DOMUtils', () => {
  beforeEach(() => {
    document.body.innerHTML = '';
  });

  describe('querySelector', () => {
    it('should return element when found', () => {
      const div = createMockElement('div');
      div.id = 'test';
      document.body.appendChild(div);

      const result = DOMUtils.querySelector('#test');
      expect(result).toBe(div);
    });

    it('should throw error when element not found', () => {
      expect(() => {
        DOMUtils.querySelector('#nonexistent');
      }).toThrow('Element not found: #nonexistent');
    });
  });

  describe('querySelectorSafe', () => {
    it('should return element when found', () => {
      const div = createMockElement('div');
      div.id = 'test';
      document.body.appendChild(div);

      const result = DOMUtils.querySelectorSafe('#test');
      expect(result).toBe(div);
    });

    it('should return null when element not found', () => {
      const result = DOMUtils.querySelectorSafe('#nonexistent');
      expect(result).toBeNull();
    });
  });

  describe('addEventListener', () => {
    it('should add event listener and return cleanup function', () => {
      const element = createMockElement('div');
      const handler = jest.fn();

      const cleanup = DOMUtils.addEventListener(element, 'click', handler);

      triggerEvent(element, 'click');
      expect(handler).toHaveBeenCalled();

      cleanup();
      handler.mockClear();

      triggerEvent(element, 'click');
      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('debounce', () => {
    it('should debounce function calls', async () => {
      const fn = jest.fn();
      const debouncedFn = DOMUtils.debounce(fn, 100);

      debouncedFn('call1');
      debouncedFn('call2');
      debouncedFn('call3');

      expect(fn).not.toHaveBeenCalled();

      await new Promise((resolve) => setTimeout(resolve, 150));

      expect(fn).toHaveBeenCalledTimes(1);
      expect(fn).toHaveBeenCalledWith('call3');
    });
  });

  describe('throttle', () => {
    it('should throttle function calls', async () => {
      const fn = jest.fn();
      const throttledFn = DOMUtils.throttle(fn, 100);

      throttledFn('call1');
      throttledFn('call2');
      throttledFn('call3');

      expect(fn).toHaveBeenCalledTimes(1);
      expect(fn).toHaveBeenCalledWith('call1');

      await new Promise((resolve) => setTimeout(resolve, 150));

      throttledFn('call4');
      expect(fn).toHaveBeenCalledTimes(2);
      expect(fn).toHaveBeenCalledWith('call4');
    });
  });

  describe('ready', () => {
    it('should resolve immediately when DOM is already ready', async () => {
      Object.defineProperty(document, 'readyState', {
        value: 'complete',
        writable: true,
      });

      const startTime = Date.now();
      await DOMUtils.ready();
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(10);
    });
  });

  describe('createElement', () => {
    it('should create element with attributes and text content', () => {
      const element = DOMUtils.createElement(
        'div',
        { id: 'test', class: 'test-class' },
        'Test content'
      );

      expect(element.tagName).toBe('DIV');
      expect(element.id).toBe('test');
      expect(element.className).toBe('test-class');
      expect(element.textContent).toBe('Test content');
    });

    it('should create element with child elements', () => {
      const child1 = createMockElement('span');
      const child2 = createMockElement('p');

      const element = DOMUtils.createElement('div', {}, [child1, child2]);

      expect(element.children).toHaveLength(2);
      expect(element.children[0]).toBe(child1);
      expect(element.children[1]).toBe(child2);
    });
  });

  describe('isInViewport', () => {
    it('should return true for element in viewport', () => {
      const element = createMockElement('div');

      // Mock getBoundingClientRect
      element.getBoundingClientRect = jest.fn().mockReturnValue({
        top: 100,
        bottom: 200,
        left: 100,
        right: 200,
      });

      // Mock window dimensions
      Object.defineProperty(window, 'innerHeight', { value: 800, writable: true });
      Object.defineProperty(window, 'innerWidth', { value: 1200, writable: true });

      expect(DOMUtils.isInViewport(element)).toBe(true);
    });

    it('should return false for element outside viewport', () => {
      const element = createMockElement('div');

      element.getBoundingClientRect = jest.fn().mockReturnValue({
        top: 1000,
        bottom: 1100,
        left: 100,
        right: 200,
      });

      Object.defineProperty(window, 'innerHeight', { value: 800, writable: true });
      Object.defineProperty(window, 'innerWidth', { value: 1200, writable: true });

      expect(DOMUtils.isInViewport(element)).toBe(false);
    });
  });

  describe('parseDataAttribute', () => {
    it('should parse valid JSON from data attribute', () => {
      const element = createMockElement('div');
      element.dataset.config = '{"test": true}';

      const result = DOMUtils.parseDataAttribute(element, 'config', {});
      expect(result).toEqual({ test: true });
    });

    it('should return fallback for invalid JSON', () => {
      const element = createMockElement('div');
      element.dataset.config = 'invalid json';

      const fallback = { default: true };
      const result = DOMUtils.parseDataAttribute(element, 'config', fallback);
      expect(result).toBe(fallback);
    });

    it('should return fallback for missing attribute', () => {
      const element = createMockElement('div');

      const fallback = { default: true };
      const result = DOMUtils.parseDataAttribute(element, 'config', fallback);
      expect(result).toBe(fallback);
    });
  });
});
