import { EventEmitter } from '../core/EventEmitter';
import { AllocationItem, CacheData } from './types';

/**
 * CacheManager - Handles localStorage operations for patrimony data persistence
 *
 * This class manages the caching of patrimony data including main value and allocations
 * with versioning, error handling, and migration utilities.
 *
 * Events emitted:
 * - 'dataLoaded': When data is successfully loaded from cache
 * - 'dataSaved': When data is successfully saved to cache
 * - 'cacheCleared': When cache is cleared
 * - 'migrationCompleted': When data migration is completed
 * - 'error': When cache operations fail
 */
export class CacheManager extends EventEmitter {
  private static instance: CacheManager;
  private readonly CACHE_PREFIX = 'patrimony_';
  private readonly MAIN_CACHE_KEY = 'patrimony_main_data';
  private readonly VERSION_KEY = 'patrimony_cache_version';
  private readonly CURRENT_VERSION = '1.0.0';
  private readonly MAX_CACHE_AGE = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

  private constructor() {
    super();
  }

  /**
   * Get the singleton instance of CacheManager
   * @returns CacheManager instance
   */
  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Save patrimony data to localStorage
   * @param mainValue Main patrimony value
   * @param allocations Map of allocation items
   * @returns Promise that resolves when data is saved
   */
  async saveData(mainValue: number, allocations: Map<number, AllocationItem>): Promise<void> {
    try {
      const cacheData: CacheData = {
        mainValue,
        allocations,
        timestamp: Date.now(),
        version: this.CURRENT_VERSION,
      };

      const serializedData = this.serializeData(cacheData);

      // Check localStorage availability and quota
      this.checkStorageAvailability();

      localStorage.setItem(this.MAIN_CACHE_KEY, serializedData);
      localStorage.setItem(this.VERSION_KEY, this.CURRENT_VERSION);

      this.emit('dataSaved', {
        mainValue,
        allocationCount: allocations.size,
        timestamp: cacheData.timestamp,
      });
    } catch (error) {
      this.handleError('Failed to save data to cache', error);
      throw error;
    }
  }

  /**
   * Load patrimony data from localStorage
   * @returns Promise that resolves with cached data or null if not found
   */
  async loadData(): Promise<CacheData | null> {
    try {
      // Check if localStorage is available
      if (!this.isStorageAvailable()) {
        return null;
      }

      const cachedVersion = localStorage.getItem(this.VERSION_KEY);
      const rawData = localStorage.getItem(this.MAIN_CACHE_KEY);

      if (!rawData) {
        return null;
      }

      // Check version compatibility
      if (cachedVersion !== this.CURRENT_VERSION) {
        const migrated = await this.migrateData(rawData, cachedVersion || '0.0.0');
        if (migrated) {
          return migrated;
        } else {
          // Migration failed, clear cache
          this.clearCache();
          return null;
        }
      }

      const cacheData = this.deserializeData(rawData);

      // Check cache age
      if (this.isCacheExpired(cacheData.timestamp)) {
        this.clearCache();
        return null;
      }

      this.emit('dataLoaded', {
        mainValue: cacheData.mainValue,
        allocationCount: cacheData.allocations.size,
        timestamp: cacheData.timestamp,
        age: Date.now() - cacheData.timestamp,
      });

      return cacheData;
    } catch (error) {
      this.handleError('Failed to load data from cache', error);
      // Clear corrupted cache
      this.clearCache();
      return null;
    }
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    try {
      const keysToRemove: string[] = [];

      // Find all keys with our prefix
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.CACHE_PREFIX)) {
          keysToRemove.push(key);
        }
      }

      // Remove main cache keys
      keysToRemove.push(this.MAIN_CACHE_KEY, this.VERSION_KEY);

      // Remove all found keys
      keysToRemove.forEach((key) => {
        localStorage.removeItem(key);
      });

      this.emit('cacheCleared', {
        keysRemoved: keysToRemove.length,
      });
    } catch (error) {
      this.handleError('Failed to clear cache', error);
    }
  }

  /**
   * Get cache statistics
   * @returns Cache statistics object
   */
  getCacheStats(): {
    hasData: boolean;
    version: string | null;
    age: number | null;
    size: number;
    isExpired: boolean;
  } {
    try {
      const version = localStorage.getItem(this.VERSION_KEY);
      const rawData = localStorage.getItem(this.MAIN_CACHE_KEY);

      if (!rawData) {
        return {
          hasData: false,
          version: null,
          age: null,
          size: 0,
          isExpired: false,
        };
      }

      const data = this.deserializeData(rawData);
      const age = Date.now() - data.timestamp;
      const size = new Blob([rawData]).size;

      return {
        hasData: true,
        version,
        age,
        size,
        isExpired: this.isCacheExpired(data.timestamp),
      };
    } catch (error) {
      this.handleError('Failed to get cache stats', error);
      return {
        hasData: false,
        version: null,
        age: null,
        size: 0,
        isExpired: true,
      };
    }
  }

  /**
   * Serialize cache data to string
   * @param data Cache data to serialize
   * @returns Serialized string
   */
  private serializeData(data: CacheData): string {
    // Convert Map to array for serialization
    const serializable = {
      ...data,
      allocations: Array.from(data.allocations.entries()),
    };

    return JSON.stringify(serializable);
  }

  /**
   * Deserialize string to cache data
   * @param serializedData Serialized data string
   * @returns Deserialized cache data
   */
  private deserializeData(serializedData: string): CacheData {
    const parsed = JSON.parse(serializedData);

    // Convert array back to Map
    const allocations = new Map<number, AllocationItem>(parsed.allocations);

    return {
      mainValue: parsed.mainValue,
      allocations,
      timestamp: parsed.timestamp,
      version: parsed.version,
    };
  }

  /**
   * Check if cache is expired
   * @param timestamp Cache timestamp
   * @returns True if cache is expired
   */
  private isCacheExpired(timestamp: number): boolean {
    return Date.now() - timestamp > this.MAX_CACHE_AGE;
  }

  /**
   * Check if localStorage is available
   * @returns True if localStorage is available
   */
  private isStorageAvailable(): boolean {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check localStorage availability and quota
   * @throws Error if storage is not available or quota exceeded
   */
  private checkStorageAvailability(): void {
    if (!this.isStorageAvailable()) {
      throw new Error('localStorage is not available');
    }

    // Try to estimate storage usage
    try {
      const testData = 'x'.repeat(1024); // 1KB test
      const testKey = '__quota_test__';
      localStorage.setItem(testKey, testData);
      localStorage.removeItem(testKey);
    } catch (error) {
      throw new Error('localStorage quota exceeded');
    }
  }

  /**
   * Migrate data from older versions
   * @param rawData Raw cached data
   * @param fromVersion Version to migrate from
   * @returns Migrated data or null if migration failed
   */
  private async migrateData(rawData: string, fromVersion: string): Promise<CacheData | null> {
    try {
      let migratedData: CacheData | null = null;

      // Migration logic based on version
      switch (fromVersion) {
        case '0.0.0':
        case '0.1.0':
          migratedData = this.migrateFromV0(rawData);
          break;
        default:
          // Unknown version, cannot migrate
          return null;
      }

      if (migratedData) {
        // Save migrated data
        await this.saveData(migratedData.mainValue, migratedData.allocations);

        this.emit('migrationCompleted', {
          fromVersion,
          toVersion: this.CURRENT_VERSION,
          allocationCount: migratedData.allocations.size,
        });
      }

      return migratedData;
    } catch (error) {
      this.handleError(`Failed to migrate data from version ${fromVersion}`, error);
      return null;
    }
  }

  /**
   * Migrate data from version 0.x.x
   * @param rawData Raw data string
   * @returns Migrated cache data
   */
  private migrateFromV0(rawData: string): CacheData | null {
    try {
      const oldData = JSON.parse(rawData);

      // Assume old format had different structure
      const allocations = new Map<number, AllocationItem>();

      // Convert old allocation format to new format
      if (oldData.allocations && Array.isArray(oldData.allocations)) {
        oldData.allocations.forEach((item: any, index: number) => {
          allocations.set(index, {
            index,
            category: item.category || 'Unknown',
            subcategory: item.subcategory || 'Unknown',
            value: item.value || 0,
            percentage: item.percentage || 0,
            maxAllowed: item.maxAllowed || 100,
          });
        });
      }

      return {
        mainValue: oldData.mainValue || 0,
        allocations,
        timestamp: Date.now(),
        version: this.CURRENT_VERSION,
      };
    } catch (error) {
      this.handleError('Failed to migrate from v0', error);
      return null;
    }
  }

  /**
   * Handle cache operation errors
   * @param message Error message
   * @param error Original error
   */
  private handleError(message: string, error: any): void {
    const errorInfo = {
      message,
      originalError: error instanceof Error ? error.message : String(error),
      timestamp: Date.now(),
    };

    console.error(`CacheManager Error: ${message}`, error);
    this.emit('error', errorInfo);
  }

  /**
   * Export cache data for backup
   * @returns Exported data as JSON string
   */
  async exportData(): Promise<string | null> {
    try {
      const data = await this.loadData();
      if (!data) {
        return null;
      }

      const exportData = {
        ...data,
        exportedAt: Date.now(),
        exportVersion: this.CURRENT_VERSION,
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      this.handleError('Failed to export data', error);
      return null;
    }
  }

  /**
   * Import cache data from backup
   * @param importData JSON string of exported data
   * @returns True if import was successful
   */
  async importData(importData: string): Promise<boolean> {
    try {
      const data = JSON.parse(importData);

      // Validate import data structure
      if (!data.mainValue && data.mainValue !== 0) {
        throw new Error('Invalid import data: missing mainValue');
      }

      if (!data.allocations) {
        throw new Error('Invalid import data: missing allocations');
      }

      // Convert allocations if needed
      let allocations: Map<number, AllocationItem>;
      if (Array.isArray(data.allocations)) {
        allocations = new Map(data.allocations);
      } else {
        allocations = new Map();
      }

      // Save imported data
      await this.saveData(data.mainValue, allocations);

      this.emit('dataImported', {
        mainValue: data.mainValue,
        allocationCount: allocations.size,
        importedAt: Date.now(),
      });

      return true;
    } catch (error) {
      this.handleError('Failed to import data', error);
      return false;
    }
  }

  /**
   * Get cache key for specific data type
   * @param dataType Type of data
   * @returns Cache key
   */
  private getCacheKey(dataType: string): string {
    return `${this.CACHE_PREFIX}${dataType}`;
  }

  /**
   * Set cache expiration time
   * @param maxAge Maximum age in milliseconds
   */
  setCacheMaxAge(maxAge: number): void {
    if (maxAge > 0) {
      (this as any).MAX_CACHE_AGE = maxAge;
    }
  }

  /**
   * Get current cache version
   * @returns Current cache version
   */
  getCurrentVersion(): string {
    return this.CURRENT_VERSION;
  }
}
