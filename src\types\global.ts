// Global types used across the application
export enum ErrorType {
  CURRENCY_FORMAT = 'CURRENCY_FORMAT',
  ALLOCATION_OVERFLOW = 'ALLOCATION_OVERFLOW',
  OPENAI_API = 'OPENAI_API',
  ANIMATION_FAILED = 'ANIMATION_FAILED',
  DOM_NOT_FOUND = 'DOM_NOT_FOUND',
}

export interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: Date;
}

export interface AppConfig {
  debug: boolean;
  version: string;
  environment: 'development' | 'production';
}
