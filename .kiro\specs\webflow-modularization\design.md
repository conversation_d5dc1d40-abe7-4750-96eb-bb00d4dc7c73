# Design Document

## Overview

Este documento descreve a arquitetura para modularizar o código JavaScript inline do site Webflow em uma estrutura TypeScript organizada e reutilizável. O design mantém toda a funcionalidade existente enquanto organiza o código em módulos bem definidos, com tipos TypeScript, documentação completa e padrões modernos de desenvolvimento.

## Architecture

### Estrutura de Diretórios

```
src/
├── modules/
│   ├── currency/
│   │   ├── CurrencyFormatter.ts
│   │   ├── CurrencyController.ts
│   │   └── types.ts
│   ├── patrimony/
│   │   ├── PatrimonySync.ts
│   │   ├── AllocationManager.ts
│   │   ├── CacheManager.ts
│   │   └── types.ts
│   ├── animations/
│   │   ├── MotionController.ts
│   │   ├── ButtonAnimations.ts
│   │   ├── ProductAnimations.ts
│   │   └── types.ts
│   ├── visibility/
│   │   ├── SectionVisibilityController.ts
│   │   └── types.ts
│   ├── openai/
│   │   ├── OpenAIController.ts
│   │   ├── AllocationParser.ts
│   │   └── types.ts
│   ├── products/
│   │   ├── ProductManager.ts
│   │   ├── ProductItem.ts
│   │   └── types.ts
│   ├── charts/
│   │   ├── ChartController.ts
│   │   └── types.ts
│   └── core/
│       ├── EventEmitter.ts
│       ├── DOMUtils.ts
│       ├── Utils.ts
│       └── types.ts
├── types/
│   ├── global.ts
│   └── events.ts
├── config/
│   └── constants.ts
└── index.ts
```

### Padrões de Design

1. **Module Pattern**: Cada funcionalidade é encapsulada em seu próprio módulo
2. **Observer Pattern**: Sistema de eventos para comunicação entre módulos
3. **Singleton Pattern**: Para controladores globais como PatrimonySync
4. **Factory Pattern**: Para criação de instâncias de ProductItem
5. **Strategy Pattern**: Para diferentes tipos de animações e formatações

## Components and Interfaces

### Core Types

```typescript
// src/types/global.ts
export interface CurrencyValue {
  value: number;
  formatted: string;
  currency: string;
}

export interface AllocationItem {
  index: number;
  category: string;
  subcategory: string;
  value: number;
  percentage: number;
  maxAllowed: number;
}

export interface AnimationConfig {
  duration: {
    fast: number;
    normal: number;
    slow: number;
  };
  delay: {
    deactivate: number;
    display: number;
  };
  animation: {
    blur: number;
    move: number;
    rotate: number;
  };
  ease: string;
}

export interface PatrimonyState {
  mainValue: number;
  totalAllocated: number;
  remaining: number;
  isFullyAllocated: boolean;
  isOverAllocated: boolean;
  percentageAllocated: number;
}
```

### Currency Module

```typescript
// src/modules/currency/CurrencyFormatter.ts
export class CurrencyFormatter {
  private static instance: CurrencyFormatter;
  private locale: string = 'pt-BR';
  private currency: string = 'BRL';

  static getInstance(): CurrencyFormatter;
  formatValue(value: number): string;
  parseValue(formattedValue: string): number;
  formatCurrency(value: number): string;
  createCurrencyObject(value: number): CurrencyValue;
}

// src/modules/currency/CurrencyController.ts
export class CurrencyController {
  private formatter: CurrencyFormatter;
  private inputs: Map<HTMLInputElement, CurrencyInputConfig>;

  constructor();
  initializeInput(input: HTMLInputElement, config?: CurrencyInputConfig): void;
  handleInput(input: HTMLInputElement, value: string): void;
  setupControlButtons(input: HTMLInputElement): void;
  private getIncrement(value: number): number;
  private updateValue(input: HTMLInputElement, newValue: number): void;
}
```

### Patrimony Module

```typescript
// src/modules/patrimony/PatrimonySync.ts
export class PatrimonySync extends EventEmitter {
  private static instance: PatrimonySync;
  private mainValue: number = 0;
  private allocations: Map<number, AllocationItem>;
  private cacheManager: CacheManager;

  static getInstance(): PatrimonySync;
  getMainValue(): number;
  setMainValue(value: number): void;
  updateAllocation(index: number, value: number): void;
  validateAllocations(): void;
  getTotalAllocated(): number;
  getRemainingValue(): number;
  getState(): PatrimonyState;
}

// src/modules/patrimony/AllocationManager.ts
export class AllocationManager {
  private items: ProductItem[];
  private patrimonySync: PatrimonySync;

  constructor(patrimonySync: PatrimonySync);
  initializeItems(): void;
  updateAllAllocations(): void;
  validateAllocation(item: ProductItem): void;
  getTotalAllocatedExcept(excludeItem: ProductItem): number;
}
```

### Animation Module

```typescript
// src/modules/animations/MotionController.ts
export class MotionController {
  private static instance: MotionController;
  private config: AnimationConfig;

  static getInstance(): MotionController;
  initializeMotion(): Promise<void>;
  animateElement(element: HTMLElement, properties: any, options?: any): Promise<void>;
  createHoverEffect(element: HTMLElement, hoverProps: any, restoreProps?: any): void;
  createPressEffect(element: HTMLElement, pressProps: any, restoreProps?: any): void;
}

// src/modules/animations/ButtonAnimations.ts
export class ButtonAnimations {
  private motionController: MotionController;

  constructor();
  setupCurrencyButtons(container: HTMLElement): void;
  setupHeroButtons(): void;
  private createRippleEffect(element: HTMLElement, color: string): void;
}
```

### Product Module

```typescript
// src/modules/products/ProductItem.ts
export class ProductItem extends EventEmitter {
  private element: HTMLElement;
  private index: number;
  private state: ProductItemState;
  private activeDiv: HTMLElement;
  private disabledDiv: HTMLElement;
  private input: HTMLInputElement;
  private slider: HTMLElement;

  constructor(element: HTMLElement, index: number);
  activate(): Promise<void>;
  deactivate(): Promise<void>;
  togglePin(): void;
  private setupEvents(): void;
  private scheduleDeactivate(): void;
}

// src/modules/products/ProductManager.ts
export class ProductManager extends EventEmitter {
  private items: ProductItem[];
  private activeItem: ProductItem | null;

  constructor();
  initializeItems(): void;
  getItems(): ProductItem[];
  getActiveItem(): ProductItem | null;
  private handleItemActivation(item: ProductItem): void;
}
```

### OpenAI Module

```typescript
// src/modules/openai/OpenAIController.ts
export class OpenAIController extends EventEmitter {
  private apiKey: string | null;
  private config: OpenAIConfig;
  private parser: AllocationParser;

  constructor();
  initialize(): Promise<void>;
  processAllocationRequest(prompt: string): Promise<AllocationData>;
  private callOpenAI(prompt: string, context: any): Promise<string>;
  private getApiKey(): Promise<string>;
}

// src/modules/openai/AllocationParser.ts
export class AllocationParser {
  parseResponse(response: string): AllocationData | null;
  validateAllocations(data: AllocationData): boolean;
  private extractJSON(response: string): any;
}
```

## Data Models

### Currency Data Models

```typescript
interface CurrencyInputConfig {
  isMain?: boolean;
  allowNegative?: boolean;
  maxValue?: number;
  minValue?: number;
  onValueChange?: (value: CurrencyValue) => void;
}

interface CurrencyChangeEvent {
  value: number;
  formatted: string;
  input: HTMLInputElement;
}
```

### Patrimony Data Models

```typescript
interface AllocationChangeEvent {
  index: number;
  value: number;
  percentage: number;
  formatted: string;
  remaining: number;
}

interface PatrimonyStatusEvent {
  mainValue: number;
  totalAllocated: number;
  remaining: number;
  isFullyAllocated: boolean;
  isOverAllocated: boolean;
  percentageAllocated: number;
}
```

### Product Data Models

```typescript
interface ProductItemState {
  active: boolean;
  interacting: boolean;
  sliderDragging: boolean;
  animating: boolean;
  pinned: boolean;
}

interface ProductActivationEvent {
  item: ProductItem;
  index: number;
  state: ProductItemState;
}
```

### OpenAI Data Models

```typescript
interface OpenAIConfig {
  apiEndpoint: string;
  model: string;
  maxTokens: number;
  temperature: number;
  systemPrompt: string;
  retryAttempts: number;
  retryDelay: number;
}

interface AllocationData {
  allocations: Array<{
    category: string;
    subcategory: string;
    percentage: number;
  }>;
  totalPercentage: number;
}
```

## Error Handling

### Error Types

```typescript
enum ErrorType {
  CURRENCY_FORMAT = 'CURRENCY_FORMAT',
  ALLOCATION_OVERFLOW = 'ALLOCATION_OVERFLOW',
  OPENAI_API = 'OPENAI_API',
  ANIMATION_FAILED = 'ANIMATION_FAILED',
  DOM_NOT_FOUND = 'DOM_NOT_FOUND',
}

interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: Date;
}
```

### Error Handling Strategy

1. **Graceful Degradation**: Se um módulo falhar, outros continuam funcionando
2. **User Feedback**: Erros visíveis ao usuário são mostrados via notificações
3. **Logging**: Todos os erros são logados para debugging
4. **Retry Logic**: APIs externas têm retry automático
5. **Fallbacks**: Funcionalidades têm fallbacks quando possível

## Testing Strategy

### Unit Tests

- Cada módulo terá testes unitários completos
- Mocking de dependências externas (DOM, APIs)
- Cobertura mínima de 80%

### Integration Tests

- Testes de integração entre módulos
- Simulação de interações do usuário
- Validação de fluxos completos

### E2E Tests

- Testes end-to-end usando Playwright
- Cenários de uso real
- Validação de funcionalidades críticas

### Test Structure

```
tests/
├── unit/
│   ├── currency/
│   ├── patrimony/
│   ├── animations/
│   └── ...
├── integration/
│   ├── currency-patrimony.test.ts
│   ├── openai-allocation.test.ts
│   └── ...
└── e2e/
    ├── full-allocation-flow.spec.ts
    ├── currency-interaction.spec.ts
    └── ...
```

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Módulos carregados sob demanda
2. **Debouncing**: Inputs com debounce para performance
3. **Event Delegation**: Uso eficiente de event listeners
4. **Memory Management**: Cleanup adequado de recursos
5. **Animation Performance**: Uso de requestAnimationFrame e GPU acceleration

### Monitoring

- Performance metrics para operações críticas
- Memory usage monitoring
- Animation frame rate tracking
- API response time monitoring

## Security Considerations

### Data Protection

1. **API Keys**: Armazenamento seguro no localStorage com encryption
2. **Input Validation**: Sanitização de todos os inputs
3. **XSS Prevention**: Escape de conteúdo dinâmico
4. **CSRF Protection**: Tokens para operações sensíveis

### Privacy

- Dados financeiros não são enviados para APIs externas sem consentimento
- Cache local com opção de limpeza
- Logs não contêm informações sensíveis
