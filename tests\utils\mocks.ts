// Mock implementations for external dependencies

/**
 * Mock Motion.js library
 */
export const mockMotion = {
  animate: jest.fn().mockResolvedValue(undefined),
  timeline: jest.fn().mockReturnValue({
    add: jest.fn(),
    play: jest.fn(),
    pause: jest.fn(),
    reverse: jest.fn(),
    restart: jest.fn(),
  }),
  stagger: jest.fn(),
  spring: jest.fn(),
  inView: jest.fn(),
};

/**
 * Mock GSAP library
 */
export const mockGSAP = {
  to: jest.fn(),
  from: jest.fn(),
  fromTo: jest.fn(),
  set: jest.fn(),
  timeline: jest.fn().mockReturnValue({
    to: jest.fn(),
    from: jest.fn(),
    fromTo: jest.fn(),
    set: jest.fn(),
    add: jest.fn(),
    play: jest.fn(),
    pause: jest.fn(),
    reverse: jest.fn(),
    restart: jest.fn(),
  }),
  registerPlugin: jest.fn(),
};

/**
 * Mock OpenAI API response
 */
export const mockOpenAIResponse = {
  choices: [
    {
      message: {
        content: JSON.stringify({
          allocations: [
            { category: 'Ações', subcategory: 'Nacionais', percentage: 40 },
            { category: 'Renda Fixa', subcategory: 'CDB', percentage: 30 },
            { category: 'Fundos', subcategory: 'Multimercado', percentage: 30 },
          ],
          totalPercentage: 100,
        }),
      },
    },
  ],
};

/**
 * Mock fetch for API calls
 */
export const mockFetch = jest.fn().mockResolvedValue({
  ok: true,
  status: 200,
  json: () => Promise.resolve(mockOpenAIResponse),
});

/**
 * Mock Intersection Observer
 */
export class MockIntersectionObserver {
  private callback: IntersectionObserverCallback;
  private elements: Set<Element> = new Set();

  constructor(callback: IntersectionObserverCallback) {
    this.callback = callback;
  }

  observe(element: Element): void {
    this.elements.add(element);
  }

  unobserve(element: Element): void {
    this.elements.delete(element);
  }

  disconnect(): void {
    this.elements.clear();
  }

  // Helper method to trigger intersection events in tests
  triggerIntersection(element: Element, isIntersecting: boolean): void {
    const entry: IntersectionObserverEntry = {
      target: element,
      isIntersecting,
      intersectionRatio: isIntersecting ? 1 : 0,
      intersectionRect: element.getBoundingClientRect(),
      boundingClientRect: element.getBoundingClientRect(),
      rootBounds: null,
      time: Date.now(),
    };
    this.callback([entry], this);
  }
}

/**
 * Mock localStorage with additional testing utilities
 */
export class MockLocalStorage {
  private store: Map<string, string> = new Map();

  getItem(key: string): string | null {
    return this.store.get(key) || null;
  }

  setItem(key: string, value: string): void {
    this.store.set(key, value);
  }

  removeItem(key: string): void {
    this.store.delete(key);
  }

  clear(): void {
    this.store.clear();
  }

  get length(): number {
    return this.store.size;
  }

  key(index: number): string | null {
    const keys = Array.from(this.store.keys());
    return keys[index] || null;
  }

  // Helper methods for testing
  getStore(): Map<string, string> {
    return new Map(this.store);
  }

  hasKey(key: string): boolean {
    return this.store.has(key);
  }
}

/**
 * Setup mocks for testing environment
 */
export function setupMocks(): void {
  // Mock global fetch
  global.fetch = mockFetch;

  // Mock Motion.js if imported
  jest.doMock('motion', () => mockMotion);

  // Mock GSAP if imported
  jest.doMock('gsap', () => mockGSAP);

  // Replace IntersectionObserver
  global.IntersectionObserver = MockIntersectionObserver as any;

  // Replace localStorage
  const mockStorage = new MockLocalStorage();
  Object.defineProperty(window, 'localStorage', {
    value: mockStorage,
  });
}

/**
 * Reset all mocks to their initial state
 */
export function resetMocks(): void {
  jest.clearAllMocks();
  mockFetch.mockClear();

  // Reset localStorage
  const mockStorage = new MockLocalStorage();
  Object.defineProperty(window, 'localStorage', {
    value: mockStorage,
  });
}
